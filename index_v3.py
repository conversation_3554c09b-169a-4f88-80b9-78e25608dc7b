from telethon import TelegramClient
from telethon.tl.types import MessageMediaWebPage, MessageMediaDocument, MessageMediaPhoto
import os
from dotenv import load_dotenv
import asyncio
from telethon.errors import FloodWaitError
import json
import traceback
import time
from datetime import datetime
from news_analyzer import NewsAnalyzer
from signal_analyzer import SignalAnalyzer
from trading_integration import TradingIntegration
import shutil
import argparse

load_dotenv()

# Парсинг аргументов командной строки
parser = argparse.ArgumentParser(description='Telegram Parser Bot - автоматическая пересылка и анализ сообщений')
parser.add_argument('--update-counters', action='store_true',
                   help='Обновить счетчики last_message_id до последних сообщений, затем продолжить обычную работу')
parser.add_argument('--clear-bots-logs', action='store_true',
                   help='Очистить папку bots (удалить все файлы логов)')
args = parser.parse_args()

# Вводим данные сессии
api_id = os.getenv('API_ID')
api_hash = os.getenv('API_HASH')
phone = os.getenv('PHONE')
session_name = os.getenv('SESSION_NAME')
output_file = os.getenv('OUTPUT_FILE', 'dialogs.txt')
admin_id = int(os.getenv('ADMIN_ID', 0))
long_caption_handling = os.getenv('LONG_CAPTION_HANDLING', 'separate')
admin_log_level = os.getenv('ADMIN_LOG_LEVEL', 'ERROR').upper()
show_source_header = os.getenv('SHOW_SOURCE_HEADER', 'False').lower() == 'true'
fetch_dialogs_enabled = os.getenv('FETCH_DIALOGS_ENABLED', 'False').lower().strip() == 'true'

# Максимальный процент портфеля для торговли (остальное - резерв)
max_portfolio_usage = float(os.getenv('MAX_PORTFOLIO_USAGE', 100))  # По умолчанию 100% (без ограничений)

# Настройки для работы с медиафайлами
download_media_path = os.getenv('DOWNLOAD_MEDIA_PATH', './media')
delete_media_after_send = os.getenv('DELETE_MEDIA_AFTER_SEND', 'False').lower() == 'true'
ignore_video_download = os.getenv('IGNORE_VIDEO_DOWNLOAD', 'True').lower() == 'true'

# Создаем клиент
client = TelegramClient(session_name, api_id, api_hash)

# Функция для загрузки конфигурации каналов из JSON файла
def load_source_channels():
    """
    Загружает конфигурацию каналов из source_channels.json файла.
    
    Формат конфигурации каналов:
    {
        "channels": {
            "Название канала": {
                "id": ID_канала,
                "forward_type": "custom"|"repost",
                "target_chat_id": ID_целевого_чата,
                "signal_fn": "signal_analyzer",
                "signals_only": true|false,
                "max_volume": объем_USD,
                "leverage": плечо,
                "portfolio_percent": процент_портфеля,
                "open_mode": "default"|"grid_open"|"limit_open_1_limit_exit_5",
                "move_stop_to_breakeven": true|false,
                "allow_signals_without_sl_tp": true|false,
                "position_lifetime": "2h",
                "max_profit": максимальная_прибыль,
                "review": true|false,
                "analyze_images": true|false
            }
        }
    }
    
    Параметры:
    - forward_type:
      * "custom" (по умолчанию): отправка от своего имени с анализом и обработкой текста
      * "repost": простой репост сообщений без обработки
    - target_chat_id (опционально): ID чата для пересылки сообщений из этого канала
      * если указан: сообщения пересылаются в этот конкретный чат
      * если не указан: используется глобальный TARGET_CHAT_ID из .env
      * если ни один не указан: канал работает в режиме мониторинга
    - signal_fn (опционально, только для "custom"):
      * "signal_analyzer": использовать анализатор торговых сигналов для извлечения JSON
      * None: обычная обработка без анализа сигналов
    - signals_only (опционально, только для "custom" с signal_fn):
      * true: пересылать ТОЛЬКО сообщения с торговыми сигналами на вход
      * false (по умолчанию): пересылать все сообщения
    - max_volume (для торговых ботов): максимальный объем позиции в USD (используется если portfolio_percent не задан)
    - portfolio_percent (для торговых ботов): процент от баланса USDT для позиции (приоритет над max_volume)
    - leverage (для торговых ботов): плечо для торговли (5x-15x)
    - open_mode (для торговых ботов): режим открытия позиций
    - move_stop_to_breakeven (для торговых ботов): перемещать ли стоп-лосс на безубыток после первого TP
    - allow_signals_without_sl_tp (опционально, для торговых ботов): разрешить сигналы без обязательных stop_loss и take_profit
    - position_lifetime (для торговых ботов): время жизни позиции в формате "1h30m10s", "25m", "30s", "2h30m"
    - max_profit (для торговых ботов): максимальная прибыль в процентах (0 = без ограничений)
    - review (опционально): анализировать ли сообщения на предмет рыночного обзора
    """
    try:
        with open('source_channels.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('channels', {})
    except FileNotFoundError:
        print("❌ Файл source_channels.json не найден!")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ Ошибка парсинга JSON в source_channels.json: {e}")
        return {}
    except Exception as e:
        print(f"❌ Ошибка загрузки конфигурации каналов: {e}")
        return {}

# Загружаем конфигурацию каналов из JSON файла
SOURCE_CHANNELS = load_source_channels()

# Целевой чат для пересылки (настраивается через .env)
TARGET_CHAT_ID = os.getenv('TARGET_CHAT_ID')
if TARGET_CHAT_ID:
    try:
        TARGET_CHAT_ID = int(TARGET_CHAT_ID)
    except ValueError:
        print(f"ОШИБКА: TARGET_CHAT_ID должен быть числом, получено: {os.getenv('TARGET_CHAT_ID')}")
        TARGET_CHAT_ID = None
else:
    print("⚠️  TARGET_CHAT_ID не задан в .env файле - пересылка сообщений будет отключена")
    TARGET_CHAT_ID = None

class CustomLogger:
    """Кастомный логгер для отправки сообщений в консоль и админу в Telegram"""

    def __init__(self, client, admin_id, admin_log_level='ERROR'):
        self.client = client
        self.admin_id = admin_id
        self.admin_log_level = admin_log_level
        self.is_client_ready = False

    def set_client_ready(self, status=True):
        """Устанавливает статус готовности клиента"""
        self.is_client_ready = status

    def _should_send_to_admin(self, level):
        """Проверяет, нужно ли отправлять сообщение данного уровня админу"""
        level_priority = {
            'INFO': 1,
            'SUCCESS': 2,
            'WARNING': 3,
            'ERROR': 4
        }

        admin_level_priority = {
            'ALL': 0,      # Все уровни
            'INFO': 1,     # INFO и выше
            'WARNING': 3,  # WARNING и выше
            'ERROR': 4     # Только ERROR
        }

        current_level_num = level_priority.get(level, 1)
        required_level_num = admin_level_priority.get(self.admin_log_level, 4)

        return current_level_num >= required_level_num

    async def _send_to_admin(self, message, level="INFO"):
        """Отправляет сообщение админу в Telegram"""
        if not self.admin_id or not self.is_client_ready:
            return

        # Проверяем, нужно ли отправлять сообщение данного уровня
        if not self._should_send_to_admin(level):
            return

        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            formatted_message = f"🤖 **Telegram Parser Bot**\n\n"
            formatted_message += f"**Level:** {level}\n"
            formatted_message += f"**Time:** {timestamp}\n"
            formatted_message += f"**Message:**\n{message}"

            # Ограничиваем длину сообщения
            if len(formatted_message) > 4000:
                formatted_message = formatted_message[:3900] + "\n\n... (сообщение обрезано)"

            await self.client.send_message(self.admin_id, formatted_message)
        except Exception as e:
            print(f"Ошибка при отправке сообщения админу: {e}")

    async def send_to_target_chat(self, message: str):
        """Отправляет сообщение в целевой чат (TARGET_CHAT_ID)"""
        if not TARGET_CHAT_ID or not self.is_client_ready:
            return

        try:
            # Ограничиваем длину сообщения
            if len(message) > 4000:
                message = message[:3900] + "\n\n... (сообщение обрезано)"

            await self.client.send_message(TARGET_CHAT_ID, message)
        except Exception as e:
            print(f"Ошибка при отправке сообщения в целевой чат: {e}")
            # Также отправим ошибку админу
            await self._send_to_admin(f"Ошибка отправки в целевой чат: {e}", "ERROR")

    async def info(self, message):
        """Логирует информационное сообщение"""
        print(f"INFO: {message}")
        await self._send_to_admin(message, "INFO")

    async def warning(self, message):
        """Логирует предупреждение"""
        print(f"WARNING: {message}")
        await self._send_to_admin(message, "WARNING")

    async def error(self, message, exception=None):
        """Логирует ошибку с опциональным исключением"""
        error_msg = f"ERROR: {message}"
        if exception:
            error_msg += f"\nException: {str(exception)}"
            error_msg += f"\nTraceback:\n{traceback.format_exc()}"

        print(error_msg)
        await self._send_to_admin(error_msg, "ERROR")

    async def success(self, message):
        """Логирует успешное выполнение"""
        print(f"SUCCESS: {message}")
        await self._send_to_admin(message, "SUCCESS")

# Создаем экземпляр логгера
logger = CustomLogger(client, admin_id, admin_log_level)

# Создаем экземпляры анализаторов и торговой системы
news_analyzer = None
signal_analyzer = None
trading_integration = None

async def initialize_analyzers():
    """Инициализирует анализаторы новостей и сигналов, а также торговую систему"""
    global news_analyzer, signal_analyzer, trading_integration

    # Проверяем наличие API ключа
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        await logger.error("OPENROUTER_API_KEY не найден в переменных окружения - анализаторы отключены")
        news_analyzer = None
        signal_analyzer = None
        return

    # Инициализируем анализатор новостей
    try:
        # Проверяем, включена ли аналитика в настройках
        analytics_enabled = os.getenv('LLM_ANALYTICS_ENABLED', 'False').lower() == 'true'
        if not analytics_enabled:
            await logger.info("LLM аналитика новостей отключена в настройках (LLM_ANALYTICS_ENABLED=False)")
            news_analyzer = None
        else:
            news_analyzer = NewsAnalyzer(logger=logger)

            # Проверяем настройки прокси
            use_socks5 = os.getenv('SOCKS5', 'False').lower() == 'true'
            proxy_status = "с SOCKS5 прокси" if use_socks5 and news_analyzer.http_client else "без прокси"

            await logger.success(f"Анализатор новостей успешно инициализирован {proxy_status} с {len(news_analyzer.model_manager.preferred_models)} доступными моделями")
    except Exception as e:
        await logger.error(f"Критическая ошибка при инициализации анализатора новостей: {str(e)}", e)
        news_analyzer = None

    # Инициализируем анализатор сигналов
    try:
        # Проверяем, нужен ли анализатор сигналов (есть ли каналы с signal_fn)
        signal_channels = [name for name, config in SOURCE_CHANNELS.items() 
                          if isinstance(config, dict) and config.get("signal_fn") == "signal_analyzer"]

        if not signal_channels:
            await logger.info("Анализатор торговых сигналов не требуется - нет каналов с signal_fn='signal_analyzer'")
            signal_analyzer = None
        else:
            signal_analyzer = SignalAnalyzer(logger=logger)
            await logger.success(f"Анализатор торговых сигналов успешно инициализирован для каналов: {', '.join(signal_channels)}")
    except Exception as e:
        await logger.error(f"Критическая ошибка при инициализации анализатора сигналов: {str(e)}", e)
        signal_analyzer = None

    # Инициализируем торговую систему
    try:
        trading_integration = TradingIntegration(logger=logger)
        if await trading_integration.initialize():
            if trading_integration.is_trading_enabled():
                await logger.success("🤖 Торговая система успешно инициализирована и активна")
            else:
                await logger.info("🤖 Торговая система инициализирована, но отключена")
        else:
            await logger.warning("⚠️ Торговая система не инициализирована")
            trading_integration = None
    except Exception as e:
        await logger.error(f"Критическая ошибка при инициализации торговой системы: {str(e)}", e)
        trading_integration = None

# Инициализация завершена

def get_channel_config(channel_name):
    """Получает конфигурацию канала с проверкой на старый и новый формат"""
    channel_config = SOURCE_CHANNELS.get(channel_name)

    if isinstance(channel_config, int):
        # Старый формат - просто ID канала
        return {"id": channel_config, "forward_type": "custom", "signal_fn": None, "signals_only": False, "allow_signals_without_sl_tp": False, "review": False, "target_chat_id": None, "analyze_images": False}
    elif isinstance(channel_config, dict):
        # Новый формат - словарь с настройками
        return {
            "id": channel_config.get("id"),
            "forward_type": channel_config.get("forward_type", "custom"),
            "signal_fn": channel_config.get("signal_fn"),
            "signals_only": channel_config.get("signals_only", False),
            "leverage": channel_config.get("leverage"),
            "allow_signals_without_sl_tp": channel_config.get("allow_signals_without_sl_tp", False),
            "review": channel_config.get("review", False),
            "position_lifetime": channel_config.get("position_lifetime", "0s"),
            "target_chat_id": channel_config.get("target_chat_id"),
            "analyze_images": channel_config.get("analyze_images", False)
        }
    else:
        return None

def get_target_chat_id(channel_config):
    """Определяет target chat ID для канала с fallback на глобальный TARGET_CHAT_ID"""
    # Приоритет: target_chat_id канала > глобальный TARGET_CHAT_ID > None
    channel_target = channel_config.get("target_chat_id") if channel_config else None
    return channel_target if channel_target is not None else TARGET_CHAT_ID

def get_trading_channels_configs():
    """Получает конфигурации торговых ботов из SOURCE_CHANNELS"""
    trading_configs = {}

    for channel_name, config in SOURCE_CHANNELS.items():
        if isinstance(config, dict) and config.get("signal_fn") == "signal_analyzer":
            # Это торговый канал
            trading_configs[channel_name] = {
                "max_volume": config.get("max_volume", 10),
                "leverage": config.get("leverage", 10),
                "bot_function": config.get("bot_function", "default"),
                "portfolio_percent": config.get("portfolio_percent", None),
                "max_portfolio_usage": max_portfolio_usage,  # Глобальное ограничение портфеля
                "open_mode": config.get("open_mode", "default"),  # Режим открытия позиций
                "move_stop_to_breakeven": config.get("move_stop_to_breakeven", True),  # Перемещение стоп-лосса на безубыток
                "max_profit_percent": config.get("max_profit_percent", 0.0),  # Максимальная прибыль в процентах (0 = без ограничений)
                "position_lifetime": config.get("position_lifetime", "0s")  # Время жизни позиции
            }

    return trading_configs

async def ensure_media_directory():
    """Создает папку для медиафайлов, если она не существует"""
    try:
        if not os.path.exists(download_media_path):
            os.makedirs(download_media_path)
            await logger.success(f"Создана папка для медиафайлов: {download_media_path}")
    except Exception as e:
        await logger.error(f"Ошибка при создании папки для медиафайлов: {str(e)}", e)

async def clear_bots_logs():
    """Очищает папку bots (удаляет все файлы логов)"""
    try:
        bots_dir = "./bots"
        if os.path.exists(bots_dir):
            files_removed = 0
            for filename in os.listdir(bots_dir):
                file_path = os.path.join(bots_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    files_removed += 1
            print(f"✅ Очищена папка bots: удалено {files_removed} файлов")
        else:
            print("ℹ️ Папка bots не существует")
    except Exception as e:
        print(f"❌ Ошибка при очистке папки bots: {str(e)}")

def clear_bots_logs_sync():
    """Синхронная версия функции очистки папки bots"""
    try:
        bots_dir = "./bots"
        if os.path.exists(bots_dir):
            files_removed = 0
            for filename in os.listdir(bots_dir):
                file_path = os.path.join(bots_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    files_removed += 1
            print(f"✅ Очищена папка bots: удалено {files_removed} файлов")
        else:
            print("ℹ️ Папка bots не существует")
    except Exception as e:
        print(f"❌ Ошибка при очистке папки bots: {str(e)}")

async def download_media_from_message(message):
    """Скачивает медиафайл из сообщения и возвращает путь к файлу"""
    if not message.media or isinstance(message.media, MessageMediaWebPage):
        return None

    try:
        # Определяем оригинальное имя файла
        original_filename = None
        extension = '.bin'

        if isinstance(message.media, MessageMediaPhoto):
            extension = '.jpg'
            original_filename = f"photo_{message.id}{extension}"
        elif isinstance(message.media, MessageMediaDocument):
            # Пытаемся получить оригинальное имя файла
            if hasattr(message.media.document, 'attributes'):
                for attr in message.media.document.attributes:
                    if hasattr(attr, 'file_name') and attr.file_name:
                        original_filename = attr.file_name
                        _, ext = os.path.splitext(attr.file_name)
                        extension = ext if ext else '.bin'
                        break

                # Если имя файла не найдено, определяем по MIME типу
                if not original_filename:
                    mime_type = message.media.document.mime_type
                    if mime_type:
                        if 'image' in mime_type:
                            extension = '.jpg'
                        elif 'video' in mime_type:
                            extension = '.mp4'
                        elif 'audio' in mime_type:
                            extension = '.mp3'
                        elif 'application/pdf' in mime_type:
                            extension = '.pdf'
                        else:
                            extension = '.bin'
                    original_filename = f"document_{message.id}{extension}"
            else:
                original_filename = f"document_{message.id}{extension}"
        else:
            original_filename = f"media_{message.id}{extension}"

        # Создаем безопасное имя файла (заменяем пробелы на подчеркивания, удаляем опасные символы)
        # Сначала заменяем пробелы на подчеркивания
        safe_filename = original_filename.replace(' ', '_')
        # Затем удаляем опасные символы, оставляя только буквы, цифры, точки, тире и подчеркивания
        safe_filename = "".join(c for c in safe_filename if c.isalnum() or c in '._-').rstrip()
        if not safe_filename:
            safe_filename = f"media_{message.id}{extension}"

        # Добавляем timestamp для уникальности, если файл уже существует
        file_path = os.path.join(download_media_path, safe_filename)
        if os.path.exists(file_path):
            name, ext = os.path.splitext(safe_filename)
            timestamp = int(time.time())
            safe_filename = f"{name}_{timestamp}{ext}"
            file_path = os.path.join(download_media_path, safe_filename)

        # Определяем, является ли файл видео для отслеживания прогресса
        is_video = False
        if isinstance(message.media, MessageMediaDocument):
            mime_type = getattr(message.media.document, 'mime_type', '')
            if mime_type and 'video' in mime_type:
                is_video = True

        # Проверяем настройку игнорирования видео
        if is_video and ignore_video_download:
            await logger.info(f"🚫 Видео {safe_filename} пропущено (IGNORE_VIDEO_DOWNLOAD=True)")
            return None

        # Функция для отслеживания прогресса скачивания
        last_log_time = 0
        async def progress_callback(current, total):
            nonlocal last_log_time
            current_time = time.time()

            # Логируем каждые 5 секунд
            if current_time - last_log_time >= 5:
                percent = (current / total * 100) if total > 0 else 0
                mb_current = current / (1024 * 1024)
                mb_total = total / (1024 * 1024)
                await logger.info(f"📹 Скачивание видео {safe_filename}: {percent:.1f}% ({mb_current:.1f}/{mb_total:.1f} MB)")
                last_log_time = current_time

        # Скачиваем файл с прогрессом для видео
        if is_video:
            await logger.info(f"📹 Начинаем скачивание видео: {safe_filename}")
            downloaded_file = await client.download_media(message.media, file_path, progress_callback=progress_callback)
        else:
            downloaded_file = await client.download_media(message.media, file_path)

        if downloaded_file:
            if is_video:
                await logger.success(f"📹 Видео скачано: {safe_filename}")
            else:
                await logger.info(f"Медиафайл скачан: {safe_filename}")
            return downloaded_file
        else:
            await logger.warning(f"Не удалось скачать медиафайл из сообщения {message.id}")
            return None

    except Exception as e:
        await logger.error(f"Ошибка при скачивании медиафайла из сообщения {message.id}: {str(e)}", e)
        return None

async def cleanup_media_files(file_paths):
    """Удаляет скаченные медиафайлы если включена соответствующая настройка"""
    if not delete_media_after_send or not file_paths:
        return

    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                await logger.info(f"Удален медиафайл: {file_path}")
        except Exception as e:
            await logger.error(f"Ошибка при удалении медиафайла {file_path}: {str(e)}", e)

async def main():
    try:
        print(f"🔍 DEBUG: Читаем FETCH_DIALOGS_ENABLED из .env...")
        fetch_dialogs_raw = os.getenv('FETCH_DIALOGS_ENABLED', 'False')
        print(f"🔍 DEBUG: FETCH_DIALOGS_ENABLED raw value = '{fetch_dialogs_raw}'")
        print(f"🔍 DEBUG: FETCH_DIALOGS_ENABLED после .lower().strip() = '{fetch_dialogs_raw.lower().strip()}'")
        print(f"🔍 DEBUG: fetch_dialogs_enabled = {fetch_dialogs_enabled}")

        await logger.info('Запускаем основную функцию.')
        await client.start(phone)
        logger.set_client_ready(True)
        await logger.success('Клиент Telegram успешно запущен и готов к работе')

        # Инициализируем анализаторы
        await initialize_analyzers()

        # Создаем папку для медиафайлов
        await ensure_media_directory()

        # Логируем настройки
        if long_caption_handling == 'truncate':
            await logger.info('⚙️ Настройка: длинные подписи будут ОБРЕЗАТЬСЯ до 1024 символов')
        else:
            await logger.info('⚙️ Настройка: длинные подписи будут отправляться ОТДЕЛЬНЫМИ сообщениями')

        if show_source_header:
            await logger.info('⚙️ Заголовки с источником канала: ВКЛЮЧЕНЫ')
        else:
            await logger.info('⚙️ Заголовки с источником канала: ОТКЛЮЧЕНЫ')

        if fetch_dialogs_enabled:
            await logger.info('⚙️ Получение списка диалогов: ВКЛЮЧЕНО')
        else:
            await logger.info('⚙️ Получение списка диалогов: ОТКЛЮЧЕНО')

        # Отладка: всегда показываем исходное значение
        fetch_dialogs_raw = os.getenv('FETCH_DIALOGS_ENABLED', 'False')
        await logger.info(f'⚙️ DEBUG: FETCH_DIALOGS_ENABLED = "{fetch_dialogs_raw}" -> {fetch_dialogs_enabled}')

        await logger.info(f'⚙️ Уровень логирования для админа: {admin_log_level}')

        # Логирование настроек целевого чата
        if TARGET_CHAT_ID:
            await logger.info(f'🎯 Целевой чат для пересылки: {TARGET_CHAT_ID}')
        else:
            await logger.warning('⚠️  Целевой чат не задан - пересылка сообщений отключена!')
            await logger.info('💡 Для включения пересылки добавьте TARGET_CHAT_ID в .env файл')

        # Логирование настроек медиафайлов
        await logger.info(f'⚙️ Папка для скачивания медиафайлов: {download_media_path}')
        if delete_media_after_send:
            await logger.info('⚙️ Удаление медиафайлов после отправки: ВКЛЮЧЕНО')
        else:
            await logger.info('⚙️ Удаление медиафайлов после отправки: ОТКЛЮЧЕНО')

        if ignore_video_download:
            await logger.info('⚙️ Скачивание видеофайлов: ОТКЛЮЧЕНО (только текст)')
        else:
            await logger.info('⚙️ Скачивание видеофайлов: ВКЛЮЧЕНО')

        # Логирование настроек каналов
        await logger.info('⚙️ Настройки каналов:')
        for channel_name in SOURCE_CHANNELS.keys():
            channel_config = get_channel_config(channel_name)
            if channel_config:
                forward_type = channel_config["forward_type"]
                signal_fn = channel_config.get("signal_fn")
                signals_only = channel_config.get("signals_only", False)

                mode_desc = "кастомная обработка" if forward_type == "custom" else "простой репост"
                if signal_fn == "signal_analyzer":
                    mode_desc += " + анализ сигналов"
                    if signals_only:
                        mode_desc += " (только сигналы на вход)"

                await logger.info(f'   • {channel_name}: {mode_desc}')
            else:
                await logger.error(f'   • {channel_name}: ОШИБКА КОНФИГУРАЦИИ!')

        # Демонстрация уровней логирования для админа
        if admin_log_level == 'ERROR':
            print("📧 В Telegram админу будут отправляться только ошибки")
        elif admin_log_level == 'WARNING':
            print("📧 В Telegram админу будут отправляться предупреждения и ошибки")
        elif admin_log_level == 'INFO':
            print("📧 В Telegram админу будет отправляться вся информация")
        elif admin_log_level == 'ALL':
            print("📧 В Telegram админу будут отправляться все сообщения")

        await logger.info("🔧 Для отладки анализатора новостей на сервере временно установите ADMIN_LOG_LEVEL=INFO в .env файле")
        await logger.info("🔌 Если есть проблемы с доступом к OpenRouter API, настройте SOCKS5 прокси в .env файле")

        # Получаем список диалогов только если включено в настройках
        if fetch_dialogs_enabled:
            await logger.info('Запускаем функцию для получения списка диалогов')
            await fetch_dialogs()
        else:
            await logger.info('Получение списка диалогов отключено в настройках (FETCH_DIALOGS_ENABLED=False)')

        await logger.info('Запускаем мониторинг и пересылку сообщений')
        await monitor_and_forward_messages()

    except Exception as e:
        await logger.error(f'Критическая ошибка в основной функции: {str(e)}', e)
        # Не завершаем скрипт, просто логируем ошибку
        await asyncio.sleep(60)  # Ждем минуту перед возможным перезапуском

async def load_last_message_id(channel_name):
    """Загружает ID последнего обработанного сообщения для канала."""
    # Создаем папку для хранения файлов с последними сообщениями
    os.makedirs('last_messages', exist_ok=True)

    filename = f'last_messages/last_message_{channel_name.replace(" ", "_").replace(".", "_")}.json'
    if os.path.exists(filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('last_message_id', 0)
        except Exception as e:
            await logger.error(f'Ошибка при загрузке последнего ID сообщения для {channel_name}: {str(e)}', e)
            return 0
    return 0

async def save_last_message_id(channel_name, message_id):
    """Сохраняет ID последнего обработанного сообщения для канала."""
    try:
        # Создаем папку для хранения файлов с последними сообщениями
        os.makedirs('last_messages', exist_ok=True)

        filename = f'last_messages/last_message_{channel_name.replace(" ", "_").replace(".", "_")}.json'
        data = {'last_message_id': message_id}
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        await logger.error(f'Ошибка при сохранении последнего ID сообщения для {channel_name}: {str(e)}', e)

async def update_all_last_message_ids():
    """Обновляет все файлы last_message_id на актуальные сообщения в каналах без их обработки."""
    try:
        await logger.info("🔄 Запущено обновление счетчиков последних сообщений до актуального состояния...")
        
        updated_channels = 0
        total_channels = len(SOURCE_CHANNELS)
        
        for channel_name in SOURCE_CHANNELS.keys():
            try:
                channel_config = get_channel_config(channel_name)
                if not channel_config:
                    await logger.warning(f"⚠️  Пропущен канал {channel_name} - некорректная конфигурация")
                    continue
                
                channel_id = channel_config["id"]
                
                # Получаем ID последнего сообщения в канале
                async for message in client.iter_messages(channel_id, limit=1):
                    last_message_id = message.id
                    await save_last_message_id(channel_name, last_message_id)
                    await logger.success(f"✅ {channel_name}: обновлен на ID {last_message_id}")
                    updated_channels += 1
                    break
                else:
                    await logger.warning(f"⚠️  {channel_name}: канал пуст или недоступен")
                
                # Небольшая задержка между каналами
                await asyncio.sleep(0.5)
                
            except Exception as e:
                await logger.error(f"❌ Ошибка обновления {channel_name}: {str(e)}", e)
                continue
        
        await logger.success(f"🎯 Обновление завершено: {updated_channels}/{total_channels} каналов обновлено")
        await logger.info("💡 Накопившиеся сообщения пропущены. Программа продолжит мониторинг новых сообщений.")
        
    except Exception as e:
        await logger.error(f"❌ Критическая ошибка при обновлении счетчиков: {str(e)}", e)

async def get_new_messages_from_channel(channel_id, channel_name, last_message_id):
    """Получает новые сообщения из канала начиная с определенного ID."""
    new_messages = []
    try:
        # Получаем сообщения начиная с последнего обработанного
        async for message in client.iter_messages(channel_id, min_id=last_message_id):
            new_messages.append(message)

        # Сортируем по ID (от старых к новым)
        new_messages.sort(key=lambda x: x.id)
        await logger.info(f"Найдено {len(new_messages)} новых сообщений в канале {channel_name}")

    except Exception as e:
        await logger.error(f"Ошибка при получении сообщений из канала {channel_name}: {str(e)}", e)

    return new_messages

def clean_text_for_channel(text, channel_name):
    """Очищает текст сообщения для определенного канала."""
    if channel_name == "Full-Time Trading" and text:
        # Убираем рекламную ссылку FTT
        text = text.replace("👉** FTT - подписаться**", "")
        text = text.replace("👉 FTT - подписаться", "")
        text = text.replace("FTT - подписаться", "")
        text = text.replace("👉** **", "")  # Убираем пустую строку
        text = text.replace("👉 ** **", "")  # Вариант с пробелом
        text = text.replace("👉****", "")  # Вариант без пробелов
        # Убираем лишние переносы строк и пустые строки
        lines = text.split('\n')
        lines = [line.strip() for line in lines if line.strip()]
        text = '\n'.join(lines)
    return text

def truncate_caption(text, max_length=1024):
    """Обрезает текст до максимально допустимой длины для подписи в Telegram."""
    if not text:
        return text

    if len(text) <= max_length:
        return text

    # Ищем последний пробел, чтобы не обрезать слово посередине
    cut_position = max_length - 3  # Оставляем место для многоточия
    last_space = text.rfind(' ', 0, cut_position)

    if last_space > 0 and last_space > cut_position - 50:  # Если пробел не слишком далеко
        truncated = text[:last_space] + "..."
    else:
        # Если подходящего пробела нет, просто обрезаем
        truncated = text[:cut_position] + "..."

    print(f"⚠️  Подпись обрезана с {len(text)} до {len(truncated)} символов")
    return truncated


async def analyze_message_if_needed(message, channel_name, channel_config):
    """Анализирует сообщение с помощью ИИ, если анализатор доступен"""
    global news_analyzer

    if not news_analyzer:
        # Не логируем предупреждение каждый раз, если аналитика отключена в настройках
        # Это информируется только при инициализации
        return None

    if not message.text:
        return None

    try:
        # Анализируем новость (каждый раз заново - никакого кеширования)
        await logger.info(f"🔍 Начинаем анализ сообщения {message.id} из {channel_name} (длина текста: {len(message.text)} символов)")
        analysis = await news_analyzer.analyze_news(message.text)

        if analysis:
            # Логируем результат анализа
            tickers_str = ", ".join(analysis.tickers) if analysis.tickers else "нет"
            tags_str = ", ".join(analysis.tags) if analysis.tags else "нет"
            sentiment_emoji = "😊" if analysis.sentiment and analysis.sentiment > 0.1 else "😞" if analysis.sentiment and analysis.sentiment < -0.1 else "😐"
            sentiment_str = f"{analysis.sentiment:.2f}" if analysis.sentiment is not None else "не определено"

            await logger.info(
                f"📊 Анализ новости из {channel_name} (ID: {message.id}): "
                f"Тикеры: {tickers_str}, Теги: {tags_str}, "
                f"Настроение: {sentiment_str} {sentiment_emoji}, "
                f"Модель: {analysis.analysis_model}"
            )

            return analysis
        else:
            await logger.warning(f"❌ Анализ сообщения {message.id} из {channel_name} не дал результатов (пустой ответ от анализатора)")

    except Exception as e:
        await logger.error(f"Ошибка при анализе сообщения {message.id} из {channel_name}: {str(e)}", e)

    return None

async def extract_signal_if_needed(message, channel_name, channel_config):
    """Извлекает торговый сигнал из сообщения, если настроен анализатор сигналов"""
    global signal_analyzer

    # Проверяем, нужно ли извлекать сигнал для этого канала
    if not channel_config.get("signal_fn") == "signal_analyzer":
        return None

    if not signal_analyzer:
        await logger.warning(f"Анализатор сигналов не инициализирован для канала {channel_name}")
        return None

    # Проверяем наличие текста или медиа
    if not message.text and not message.media:
        return None

    try:
        await logger.info(f"🎯 Начинаем извлечение торгового сигнала из сообщения {message.id} из {channel_name}")

        # Передаем настройку канала в анализатор
        allow_without_sl_tp = channel_config.get("allow_signals_without_sl_tp", False)
        analyze_images = channel_config.get("analyze_images", False)
        
        signal = None
        
        # Если включен анализ изображений и есть медиафайл
        if analyze_images and message.media and not isinstance(message.media, MessageMediaWebPage):
            try:
                # Скачиваем изображение
                downloaded_image = await download_media_from_message(message)
                
                if downloaded_image:
                    # Проверяем, что это действительно изображение
                    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
                    file_extension = os.path.splitext(downloaded_image)[1].lower()
                    
                    if file_extension in image_extensions:
                        await logger.info(f"🖼️ Анализируем изображение: {os.path.basename(downloaded_image)}")
                        
                        # Используем анализ с изображением
                        signal = await signal_analyzer.extract_signal_with_image(
                            message.text or "", 
                            downloaded_image, 
                            message.date, 
                            channel_name, 
                            allow_without_sl_tp
                        )
                        
                        # Удаляем скачанное изображение если включена настройка
                        if delete_media_after_send:
                            try:
                                os.remove(downloaded_image)
                                await logger.info(f"🗑️ Удалено изображение после анализа: {os.path.basename(downloaded_image)}")
                            except Exception as e:
                                await logger.warning(f"Ошибка удаления изображения {downloaded_image}: {e}")
                    else:
                        await logger.info(f"📄 Медиафайл не является изображением: {file_extension}")
                        # Fallback на обычный анализ текста
                        if message.text:
                            signal = await signal_analyzer.extract_signal(message.text, message.date, channel_name, allow_without_sl_tp)
                else:
                    await logger.warning(f"⚠️ Не удалось скачать медиафайл из сообщения {message.id}")
                    # Fallback на обычный анализ текста
                    if message.text:
                        signal = await signal_analyzer.extract_signal(message.text, message.date, channel_name, allow_without_sl_tp)
                        
            except Exception as e:
                await logger.error(f"Ошибка при анализе изображения из сообщения {message.id}: {str(e)}", e)
                # Fallback на обычный анализ текста
                if message.text:
                    signal = await signal_analyzer.extract_signal(message.text, message.date, channel_name, allow_without_sl_tp)
        else:
            # Обычный анализ только текста
            if message.text:
                signal = await signal_analyzer.extract_signal(message.text, message.date, channel_name, allow_without_sl_tp)

        if signal:
            # Логируем результат анализа сигнала
            direction_emoji = "📈" if signal.direction and signal.direction.value == "Long" else "📉"
            source_info = "🖼️ из изображения" if analyze_images and message.media else "📝 из текста"
            await logger.success(
                f"🎯 Торговый сигнал извлечен {source_info} из {channel_name} (ID: {message.id}): "
                f"{direction_emoji} {signal.direction} {signal.ticker}, "
                f"Плечо: {signal.leverage}x, Вход: {signal.entry_price}"
            )

            return signal
        else:
            source_info = "изображения и текста" if analyze_images and message.media else "текста"
            await logger.info(f"📭 Сообщение {message.id} из {channel_name} не содержит торгового сигнала в {source_info}")

    except Exception as e:
        await logger.error(f"Ошибка при извлечении сигнала из сообщения {message.id} из {channel_name}: {str(e)}", e)

    return None

async def analyze_review_if_needed(message, channel_name, channel_config):
    """Анализирует сообщение на предмет рыночного обзора, если включен параметр review"""
    global news_analyzer

    # Проверяем, включен ли анализ обзоров для этого канала
    if not channel_config.get("review", False):
        return None

    if not news_analyzer:
        # Анализатор новостей не инициализирован
        return None

    if not message.text:
        return None

    try:
        await logger.info(f"📋 Начинаем анализ обзора для сообщения {message.id} из {channel_name}")
        review = await news_analyzer.analyze_review(message.text)

        if review:
            # Логируем результат анализа обзора
            confidence_str = f"{review.confidence:.2f}" if review.confidence is not None else "не определена"
            review_type_str = review.review_type if review.review_type else "не определен"
            
            if review.is_review:
                await logger.success(
                    f"📋 Обзор найден в {channel_name} (ID: {message.id}): "
                    f"Тип: {review_type_str}, Уверенность: {confidence_str}, "
                    f"Модель: {review.analysis_model}"
                )
            else:
                await logger.info(f"📋 Сообщение {message.id} из {channel_name} не является обзором (уверенность: {confidence_str})")

            return review
        else:
            await logger.info(f"📋 Анализ обзора для сообщения {message.id} из {channel_name} не дал результатов")

    except Exception as e:
        await logger.error(f"Ошибка при анализе обзора сообщения {message.id} из {channel_name}: {str(e)}", e)

    return None

async def format_analysis_text(analysis, original_text):
    """Форматирует результаты анализа для добавления к сообщению"""
    if not analysis:
        return ""

    # Проверяем, есть ли хотя бы одно заполненное поле
    if not analysis.tickers and not analysis.tags and analysis.sentiment is None:
        return ""  # Если анализ пустой, не добавляем секцию

    analysis_text = "\n\n" + "─" * 10 + "\n"

    if analysis.tickers:
        analysis_text += f"📈 **Тикеры:** {', '.join(analysis.tickers)}\n"

    if analysis.tags:
        analysis_text += f"🏷️ **Теги:** {', '.join(analysis.tags)}\n"

    if analysis.sentiment is not None:
        # Эмодзи для настроения
        if analysis.sentiment > 0.1:
            sentiment_emoji = "📈 Позитивное"
            sentiment_color = "🟢"
        elif analysis.sentiment < -0.1:
            sentiment_emoji = "📉 Негативное"
            sentiment_color = "🔴"
        else:
            sentiment_emoji = "➖ Нейтральное"
            sentiment_color = "🟡"

        analysis_text += f"💭 **Настроение:** {sentiment_color} {sentiment_emoji} ({analysis.sentiment:.2f})\n"

    return analysis_text

async def format_signal_json(signal):
    """Форматирует торговый сигнал в JSON для добавления к сообщению"""
    if not signal:
        return ""

    signal_json_text = "\n\n" + "─" * 10 + "\n"
    signal_json_text += "**📊 Торговый сигнал (JSON):**\n"
    signal_json_text += "```json\n"
    signal_json_text += signal.model_dump_json(indent=2, exclude_none=True)
    signal_json_text += "\n```"

    return signal_json_text

async def repost_message_to_target(message, channel_name, channel_config):
    """Делает простой репост сообщения без обработки"""
    try:
        # Определяем target chat ID для этого канала
        target_chat_id = get_target_chat_id(channel_config)
        
        if target_chat_id is None:
            # Режим мониторинга - не пересылаем
            await logger.info(f"🔍 Репост {message.id} из {channel_name} пропущен (режим мониторинга)")
            return True

        await logger.info(f"Делаем репост сообщения {message.id} из {channel_name} в {target_chat_id}")

        source_channel_id = channel_config["id"]

        # Делаем репост сообщения
        await client.forward_messages(target_chat_id, message.id, source_channel_id)

        await logger.success(f"Сообщение {message.id} репостнуто из {channel_name} в {target_chat_id}")
        return True

    except FloodWaitError as e:
        await logger.warning(f"Слишком много запросов. Ожидание {e.seconds} секунд.")
        await asyncio.sleep(e.seconds)
        return False
    except Exception as e:
        await logger.error(f"Ошибка при репосте сообщения {message.id} из {channel_name}: {str(e)}", e)
        return False

async def send_message_to_target(message, channel_name, channel_config):
    """Отправляет сообщение в целевой чат от своего имени с указанием источника."""
    try:
        # Определяем target chat ID для этого канала
        target_chat_id = get_target_chat_id(channel_config)
        
        if target_chat_id is None:
            # Режим мониторинга - не отправляем
            message_text = message.text[:100] + "..." if message.text and len(message.text) > 100 else message.text or "[медиа без текста]"
            await logger.info(f"🔍 Мониторинг сообщения {message.id} из {channel_name}: {message_text}")
            return True

        # Анализируем сообщение
        analysis = await analyze_message_if_needed(message, channel_name, channel_config)

        # Извлекаем торговый сигнал если настроено
        signal = await extract_signal_if_needed(message, channel_name, channel_config)

        # Анализируем на предмет рыночного обзора если настроено
        review = await analyze_review_if_needed(message, channel_name, channel_config)

        # Проверяем настройку signals_only
        signals_only = channel_config.get("signals_only", False)
        if signals_only and not signal:
            # Если включен режим "только сигналы" и сигнала нет - пропускаем сообщение
            await logger.info(f"🚫 Сообщение {message.id} из {channel_name} пропущено (signals_only=True, сигнал не найден)")
            return True  # Возвращаем True чтобы сохранить last_message_id

        # Проверяем настройку review - если включена и сообщение является обзором, используем простой репост
        review_enabled = channel_config.get("review", False)
        if review_enabled and review and review.is_review:
            # Сообщение является рыночным обзором - делаем простой репост
            await logger.info(f"📋 Сообщение {message.id} из {channel_name} определено как обзор - делаем репост")
            return await repost_message_to_target(message, channel_name, channel_config)

        # Обрабатываем торговый сигнал если найден
        if signal and trading_integration and trading_integration.is_trading_enabled():
            try:
                await trading_integration.process_trading_signal(signal, channel_name)
            except Exception as e:
                await logger.error(f"Ошибка обработки торгового сигнала: {str(e)}", e)

        # Формируем текст с указанием источника (если включено в настройках)
        source_text = ""
        if show_source_header:
            source_text = f"📢 Источник: {channel_name}\n" + "─" * 10 + "\n"

        if message.text:
            # Очищаем текст для определенных каналов
            cleaned_text = clean_text_for_channel(message.text, channel_name)

            # Добавляем результаты анализа, если есть
            analysis_text = await format_analysis_text(analysis, cleaned_text)

            # Добавляем JSON торгового сигнала, если есть
            signal_json_text = await format_signal_json(signal)

            full_text = source_text + cleaned_text + analysis_text + signal_json_text
        else:
            full_text = source_text

        # Проверяем тип медиа и скачиваем, если нужно
        downloaded_file = None
        if message.media:
            # Если это веб-превью, отправляем только текст (превью создастся автоматически)
            if isinstance(message.media, MessageMediaWebPage):
                await logger.info(f"Сообщение {message.id} содержит веб-превью, отправляем только текст")
                if full_text.strip():
                    await client.send_message(target_chat_id, full_text)
                else:
                    await logger.warning(f"Сообщение {message.id} с веб-превью пустое - пропускаем")
            else:
                # Скачиваем медиафайл для обычных медиа (фото, документы и т.д.)
                downloaded_file = await download_media_from_message(message)

                if downloaded_file:
                    # Определяем, является ли файл видео для отслеживания прогресса отправки
                    is_video_upload = False
                    filename = os.path.basename(downloaded_file)
                    if any(downloaded_file.lower().endswith(ext) for ext in ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.m4v']):
                        is_video_upload = True

                    # Функция для отслеживания прогресса отправки видео
                    last_upload_log_time = 0
                    async def upload_progress_callback(current, total):
                        nonlocal last_upload_log_time
                        current_time = time.time()

                        # Логируем каждые 5 секунд
                        if current_time - last_upload_log_time >= 5:
                            percent = (current / total * 100) if total > 0 else 0
                            mb_current = current / (1024 * 1024)
                            mb_total = total / (1024 * 1024)
                            await logger.info(f"📤 Отправка видео {filename}: {percent:.1f}% ({mb_current:.1f}/{mb_total:.1f} MB)")
                            last_upload_log_time = current_time

                    if is_video_upload:
                        await logger.info(f"📤 Начинаем отправку видео: {filename}")

                    if len(full_text) > 1024:
                        if long_caption_handling == 'truncate':
                            # Обрезаем подпись до допустимой длины
                            truncated_caption = truncate_caption(full_text)
                            if is_video_upload:
                                await client.send_file(target_chat_id, downloaded_file, caption=truncated_caption, progress_callback=upload_progress_callback)
                            else:
                                await client.send_file(target_chat_id, downloaded_file, caption=truncated_caption)
                        else:
                            # Отправляем медиа без подписи и текст отдельно (по умолчанию)
                            if is_video_upload:
                                await client.send_file(target_chat_id, downloaded_file, progress_callback=upload_progress_callback)
                            else:
                                await client.send_file(target_chat_id, downloaded_file)
                            await client.send_message(target_chat_id, full_text)
                            print(f"📝 Длинный текст ({len(full_text)} символов) отправлен отдельным сообщением")
                    else:
                        # Отправляем медиа с подписью
                        if is_video_upload:
                            await client.send_file(target_chat_id, downloaded_file, caption=full_text, progress_callback=upload_progress_callback)
                        else:
                            await client.send_file(target_chat_id, downloaded_file, caption=full_text)

                    if is_video_upload:
                        await logger.success(f"📤 Видео отправлено: {filename}")
                else:
                    # Если не удалось скачать медиа, отправляем только текст (если он не пустой)
                    if full_text.strip():
                        await client.send_message(target_chat_id, full_text)
                        await logger.warning(f"Не удалось скачать медиа из сообщения {message.id}, отправлен только текст")
                    else:
                        await logger.warning(f"Сообщение {message.id} из {channel_name} пустое и без медиа - пропускаем")
        else:
            # Отправляем только текст (если он не пустой)
            if full_text.strip():
                await client.send_message(target_chat_id, full_text)
            else:
                await logger.warning(f"Сообщение {message.id} из {channel_name} пустое - пропускаем отправку")

        # Удаляем скаченный файл если включена соответствующая настройка
        if downloaded_file:
            await cleanup_media_files([downloaded_file])

        await logger.success(f"Сообщение {message.id} отправлено от своего имени из {channel_name}")
        return True

    except FloodWaitError as e:
        await logger.warning(f"Слишком много запросов. Ожидание {e.seconds} секунд.")
        await asyncio.sleep(e.seconds)
        return False
    except Exception as e:
        await logger.error(f"Ошибка при отправке сообщения {message.id} из {channel_name}: {str(e)}", e)
        return False

async def process_channel(channel_name, channel_config):
    """Обрабатывает один канал: получает новые сообщения и пересылает их."""
    try:
        channel_id = channel_config["id"]
        forward_type = channel_config["forward_type"]

        await logger.info(f"Обработка канала: {channel_name} (режим: {forward_type})")

        # Загружаем ID последнего обработанного сообщения
        last_message_id = await load_last_message_id(channel_name)

        # Если это первый запуск (last_message_id = 0), узнаем ID последнего сообщения и сохраняем его
        if last_message_id == 0:
            await logger.info(f"Первый запуск для канала {channel_name}. Получаем ID последнего сообщения...")
            try:
                # Получаем последнее сообщение в канале
                async for message in client.iter_messages(channel_id, limit=1):
                    last_message_id = message.id
                    await save_last_message_id(channel_name, last_message_id)
                    await logger.success(f"ID последнего сообщения в {channel_name}: {last_message_id}. Сохранено в файл.")
                    return  # Выходим, не пересылая сообщения при первом запуске
            except Exception as e:
                await logger.error(f"Ошибка при получении последнего сообщения из {channel_name}: {str(e)}", e)
                return

        await logger.info(f"Последний обработанный ID для {channel_name}: {last_message_id}")

        # Получаем новые сообщения
        new_messages = await get_new_messages_from_channel(channel_id, channel_name, last_message_id)

        if not new_messages:
            return  # Если нет новых сообщений, выходим

        # Обрабатываем каждое сообщение отдельно
        processed_count = 0
        for message in new_messages:
            # Определяем target chat ID для этого канала
            target_chat_id = get_target_chat_id(channel_config)
            
            # Проверяем режим работы
            if target_chat_id is None:
                # Режим мониторинга - обрабатываем, но не отправляем
                message_text = message.text[:100] + "..." if message.text and len(message.text) > 100 else message.text or "[медиа без текста]"
                await logger.info(f"🔍 Мониторинг сообщения {message.id} из {channel_name}: {message_text}")

                # В режиме мониторинга сохраняем ID сразу после обработки
                await save_last_message_id(channel_name, message.id)
                processed_count += 1
                await asyncio.sleep(0.1)  # Короткая задержка в режиме мониторинга
                continue

            # Обычный режим с пересылкой
            # Выбираем способ отправки в зависимости от настройки канала
            if forward_type == "repost":
                success = await repost_message_to_target(message, channel_name, channel_config)
            else:  # forward_type == "custom" или любое другое значение
                success = await send_message_to_target(message, channel_name, channel_config)

            if success:
                # Сохраняем ID последнего успешно обработанного сообщения
                await save_last_message_id(channel_name, message.id)
                processed_count += 1
                # Небольшая задержка между пересылками
                await asyncio.sleep(1)
            else:
                # Если не удалось отправить, прерываем обработку этого канала
                break

        # Логируем результат обработки канала
        target_chat_id = get_target_chat_id(channel_config)
        if target_chat_id is None and processed_count > 0:
            await logger.info(f"🔍 Обработано в режиме мониторинга: {processed_count} сообщений из {channel_name}")
        elif processed_count > 0:
            await logger.success(f"✅ Переслано {processed_count} сообщений из {channel_name} в {target_chat_id}")

    except Exception as e:
        await logger.error(f"Критическая ошибка при обработке канала {channel_name}: {str(e)}", e)

async def validate_configuration():
    """Проверяет корректность конфигурации перед запуском"""
    issues = []

    # Проверяем наличие активных каналов
    active_channels = [name for name in SOURCE_CHANNELS.keys() if get_channel_config(name)]
    if not active_channels:
        issues.append("❌ Нет активных каналов в SOURCE_CHANNELS")

    # Проверяем целевые чаты
    channels_with_targets = 0
    channels_without_targets = 0
    
    for channel_name in active_channels:
        channel_config = get_channel_config(channel_name)
        target_chat_id = get_target_chat_id(channel_config)
        
        if target_chat_id is not None:
            channels_with_targets += 1
        else:
            channels_without_targets += 1
    
    if channels_with_targets == 0:
        if TARGET_CHAT_ID is None:
            issues.append("⚠️  Ни один канал не имеет target_chat_id и TARGET_CHAT_ID не задан - пересылка отключена")
        else:
            issues.append("ℹ️  Все каналы будут использовать глобальный TARGET_CHAT_ID")
    elif channels_without_targets > 0:
        if TARGET_CHAT_ID is None:
            issues.append(f"⚠️  {channels_without_targets} каналов не имеют target_chat_id и будут работать в режиме мониторинга")
        else:
            issues.append(f"ℹ️  {channels_without_targets} каналов будут использовать глобальный TARGET_CHAT_ID")

    # Проверяем обязательные переменные окружения
    required_vars = ['API_ID', 'API_HASH', 'PHONE', 'SESSION_NAME']
    for var in required_vars:
        if not os.getenv(var):
            issues.append(f"❌ Отсутствует обязательная переменная окружения: {var}")

    if issues:
        await logger.warning("🔍 Обнаружены проблемы с конфигурацией:")
        for issue in issues:
            await logger.warning(f"   {issue}")

        if any("❌" in issue for issue in issues):
            await logger.error("❌ Критические ошибки конфигурации! Проверьте .env файл")
            return False
    else:
        await logger.success("✅ Конфигурация проверена успешно")

    return True

async def monitor_and_forward_messages():
    """Главная функция мониторинга и пересылки сообщений."""
    # Проверяем конфигурацию
    if not await validate_configuration():
        await logger.error("❌ Запуск невозможен из-за ошибок конфигурации")
        return

    await logger.info("Начинаем мониторинг каналов...")

    # Логируем настройки каналов
    await logger.info('⚙️ Настройки каналов:')
    for channel_name in SOURCE_CHANNELS.keys():
        channel_config = get_channel_config(channel_name)
        if channel_config:
            forward_type = channel_config["forward_type"]
            signal_fn = channel_config.get("signal_fn")
            signals_only = channel_config.get("signals_only", False)
            target_chat_id = get_target_chat_id(channel_config)

            mode_desc = "кастомная обработка" if forward_type == "custom" else "простой репост"
            if signal_fn == "signal_analyzer":
                mode_desc += " + анализ сигналов"
                if signals_only:
                    mode_desc += " (только сигналы на вход)"

            # Добавляем информацию о target канале
            if target_chat_id is not None:
                if target_chat_id == TARGET_CHAT_ID:
                    mode_desc += f" → {target_chat_id} (глобальный)"
                else:
                    mode_desc += f" → {target_chat_id} (индивидуальный)"
            else:
                mode_desc += " → мониторинг"

            await logger.info(f'   • {channel_name}: {mode_desc}')
        else:
            await logger.error(f'   • {channel_name}: ОШИБКА КОНФИГУРАЦИИ!')

    # Статистика по режимам работы
    total_channels = len(SOURCE_CHANNELS)
    channels_with_individual_targets = 0
    channels_with_global_target = 0
    channels_monitoring = 0
    
    for channel_name in SOURCE_CHANNELS.keys():
        channel_config = get_channel_config(channel_name)
        if channel_config:
            target_chat_id = get_target_chat_id(channel_config)
            if target_chat_id is not None:
                if target_chat_id == TARGET_CHAT_ID:
                    channels_with_global_target += 1
                else:
                    channels_with_individual_targets += 1
            else:
                channels_monitoring += 1
    
    await logger.info(f"📊 Статистика каналов: {channels_with_individual_targets} с индивидуальными target, {channels_with_global_target} с глобальным, {channels_monitoring} в режиме мониторинга")

    while True:
        try:
            # Обрабатываем каждый канал
            for channel_name in SOURCE_CHANNELS.keys():
                try:
                    channel_config = get_channel_config(channel_name)
                    if not channel_config:
                        await logger.error(f"Не найдена конфигурация для канала {channel_name}")
                        continue

                    await process_channel(channel_name, channel_config)
                except Exception as e:
                    await logger.error(f"Ошибка при обработке канала {channel_name}: {str(e)}", e)
                    # Продолжаем обработку следующего канала
                    continue

            # Определяем режим работы по наличию активных каналов с target'ами
            has_active_targets = False
            for channel_name in SOURCE_CHANNELS.keys():
                channel_config = get_channel_config(channel_name)
                if channel_config and get_target_chat_id(channel_config) is not None:
                    has_active_targets = True
                    break
            
            # Разная задержка в зависимости от режима
            if not has_active_targets:
                await logger.info("Цикл мониторинга завершен. Ожидание 10 секунд...")
                await asyncio.sleep(10)  # Больше задержка в режиме мониторинга
            else:
                await logger.info("Цикл обработки завершен. Ожидание 1 секунда...")
                await asyncio.sleep(1)  # Обычная задержка при пересылке

        except KeyboardInterrupt:
            await logger.info("Остановка мониторинга по запросу пользователя...")
            break
        except Exception as e:
            await logger.error(f"Ошибка в главном цикле: {str(e)}", e)
            await asyncio.sleep(60)  # Ожидание перед повтором при ошибке

async def fetch_dialogs():
    try:
        dialogs_list = []  # Переменная для хранения всех диалогов
        # Открываем файл для записи
        with open(output_file, 'w', encoding='utf-8') as f:
            while True:
                try:
                    # Используем iter_dialogs для получения всех диалогов
                    async for dialog in client.iter_dialogs(limit=None):  # Устанавливаем limit=None для получения всех диалогов
                        dialogs_list.append(dialog)
                        output_line = f"Название: {dialog.title}, ID: {dialog.id}, Тип: {type(dialog.entity).__name__}\n"
                        f.write(output_line)  # Записываем информацию в файл
                        print(output_line.strip())  # Печатаем на экран для контроля (оставляем print для отладки)

                    # Если диалоги закончились, выходим из цикла
                    break
                except FloodWaitError as e:
                    await logger.warning(f"Слишком много запросов при получении диалогов. Ожидание {e.seconds} секунд.")
                    await asyncio.sleep(e.seconds)  # Ожидаем, прежде чем повторить запрос
                except Exception as e:
                    await logger.error(f"Ошибка при получении диалогов: {str(e)}", e)
                    break  # Выход из цикла в случае ошибки

        await logger.success(f"Всего загружено диалогов: {len(dialogs_list)}")
        await logger.info(f"Вывод сохранен в файл: {output_file}")

    except Exception as e:
        await logger.error(f"Критическая ошибка при получении списка диалогов: {str(e)}", e)

async def send_message():
    try:
        await client.start(phone)

        # Используем ID админа из переменных окружения
        user_id = admin_id

        # Тестовое сообщение
        test_message = "Привет, Evgeniy! Это тестовое сообщение от бота."

        # Отправляем сообщение
        await client.send_message(user_id, test_message)
        await logger.success(f"Сообщение отправлено пользователю с ID: {user_id}")

    except Exception as e:
        await logger.error(f"Ошибка при отправке тестового сообщения: {str(e)}", e)

async def load_existing_messages(chat_id):
    """Загружает уже существующие сообщения из файла, если он существует."""
    try:
        output_file = f'chat-{abs(chat_id)}.txt'  # Имя файла для сохранения сообщений с ID чата
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                return f.readlines()
        return []
    except Exception as e:
        await logger.error(f"Ошибка при загрузке существующих сообщений для чата {chat_id}: {str(e)}", e)
        return []

async def save_messages(messages, chat_id):
    """Сохраняет новые сообщения в текстовый файл."""
    try:
        output_file = f'chat-{abs(chat_id)}.txt'  # Имя файла для сохранения сообщений с ID чата
        with open(output_file, 'a', encoding='utf-8') as f:  # Открываем файл в режиме добавления
            f.writelines(messages)
    except Exception as e:
        await logger.error(f"Ошибка при сохранении сообщений для чата {chat_id}: {str(e)}", e)

async def fetch_chat_messages(chat_id, batch_size=50):
    try:
        await client.start(phone)

        # Загружаем уже существующие сообщения
        existing_messages = await load_existing_messages(chat_id)
        existing_message_ids = {msg.split('|')[0] for msg in existing_messages}

        new_messages = []
        total_new_messages = 0  # Общее количество новых сообщений

        # Итерация по сообщениям чата
        async for message in client.iter_messages(chat_id):
            # Если сообщение уже существует, пропускаем его
            if str(message.id) not in existing_message_ids:
                new_messages.append(f"{message.id}|{message.date}|{message.sender_id}|{message.text}\n")

            # Если накоплен пакет из batch_size сообщений, записываем его в файл
            if len(new_messages) >= batch_size:
                await save_messages(new_messages, chat_id)
                total_new_messages += len(new_messages)
                await logger.info(f"Добавлено новых сообщений: {len(new_messages)}")
                new_messages = []  # Очищаем список для следующего пакета

            try:
                await asyncio.sleep(0)  # Безопасная точка для переключения контекста
            except FloodWaitError as e:
                await logger.warning(f"Слишком много запросов при получении сообщений чата. Ожидание {e.seconds} секунд.")
                await asyncio.sleep(e.seconds)

        # Записываем любые оставшиеся сообщения, если они есть
        if new_messages:
            await save_messages(new_messages, chat_id)
            total_new_messages += len(new_messages)
            await logger.info(f"Добавлено новых сообщений: {len(new_messages)}")

        await logger.success(f"Всего новых сообщений добавлено: {total_new_messages}")

    except Exception as e:
        await logger.error(f"Ошибка при получении сообщений чата {chat_id}: {str(e)}", e)

# Запуск клиента
async def run_client():
    """Главная функция запуска клиента с обработкой ошибок"""
    # Проверяем флаг обновления счетчиков ПЕРЕД основным циклом
    if args.update_counters:
        print("🔄 Режим обновления счетчиков установлен")
        print("📡 Подключение к Telegram...")
        
        try:
            await client.start(phone)
            logger.set_client_ready(True)
            await logger.success('Клиент Telegram успешно запущен для обновления счетчиков')
            
            # Обновляем все счетчики
            await update_all_last_message_ids()
            await logger.info("✅ Обновление счетчиков завершено. Переход к обычной работе...")
            
        except Exception as e:
            await logger.error(f"❌ Ошибка при обновлении счетчиков: {str(e)}", e)
            return

    # Основной цикл программы (запускается всегда - и после обновления счетчиков, и при обычном запуске)
    while True:
        try:
            if not args.update_counters:  # Логируем запуск только если не было обновления счетчиков
                await logger.info("🚀 Запуск Telegram Parser Bot...")
            await main()
        except KeyboardInterrupt:
            await logger.info("⏹️ Скрипт остановлен пользователем")
            break
        except Exception as e:
            await logger.error(f"💥 Критическая ошибка в скрипте: {str(e)}", e)
            await logger.info("🔄 Перезапуск через 5 минут...")
            await asyncio.sleep(300)  # Ждем 5 минут перед перезапуском

if __name__ == "__main__":
    # Обработка флага очистки логов ботов до запуска клиента
    if args.clear_bots_logs:
        clear_bots_logs_sync()
        print("🏁 Очистка завершена. Выход.")
        exit(0)

    with client:
        try:
            client.loop.run_until_complete(run_client())
        except KeyboardInterrupt:
            print("Скрипт остановлен пользователем")
        except Exception as e:
            print(f"Критическая ошибка при запуске: {e}")
            print(f"Traceback: {traceback.format_exc()}")
