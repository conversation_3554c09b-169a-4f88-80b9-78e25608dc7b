import os
import ccxt
import asyncio
import json
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, field, asdict
from signal_analyzer import TradingSignal, SignalDirection
import uuid

class OrderType(str, Enum):
    """Тип ордера"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_MARKET = "stop_market"
    TAKE_PROFIT_MARKET = "take_profit_market"

class OpenMode(str, Enum):
    """Режим открытия позиции"""
    DEFAULT = "default"  # Обычное открытие рыночным ордером
    GRID_OPEN = "grid_open"  # Открытие сеткой лимитных ордеров
    LIMIT_OPEN_1_LIMIT_EXIT_5 = "limit_open_1_limit_exit_5"  # Открытие лимитным ордером на 1% дальше от цены, TP 5%

class OrderStatus(str, Enum):
    """Статус ордера"""
    OPEN = "open"
    CLOSED = "closed"
    CANCELED = "canceled"
    PARTIALLY_FILLED = "partially_filled"

class TradeStatus(str, Enum):
    """Статус сделки"""
    ACTIVE = "active"
    COMPLETED = "completed"
    STOPPED = "stopped"
    ERROR = "error"

@dataclass
class TradeOrder:
    """Ордер в рамках сделки"""
    id: str
    symbol: str
    side: str  # buy/sell
    amount: float
    price: Optional[float]
    order_type: OrderType
    status: OrderStatus
    exchange_order_id: Optional[str] = None
    filled_amount: float = 0.0
    created_at: Optional[str] = None
    filled_at: Optional[str] = None

@dataclass
class TradeInstance:
    """Экземпляр торговой сделки"""
    id: str
    signal: TradingSignal
    source_channel: str
    entry_order: Optional[TradeOrder]
    stop_loss_order: Optional[TradeOrder]
    take_profit_orders: List[TradeOrder]
    status: TradeStatus
    max_volume_usd: float
    actual_volume_usd: float
    entry_price: Optional[float] = None
    current_pnl: float = 0.0
    realized_pnl: float = 0.0
    breakeven_moved: bool = False
    created_at: Optional[str] = None
    completed_at: Optional[str] = None
    log_file: Optional[str] = None
    portfolio_percent_used: Optional[float] = None  # Процент портфеля, использованный для сделки
    max_portfolio_usage: float = 100  # Максимальный процент портфеля для торговли
    actual_portfolio_percent_used: Optional[float] = None  # Фактический процент (может отличаться от запрошенного из-за лимитов)
    open_mode: OpenMode = OpenMode.DEFAULT  # Режим открытия позиции
    grid_orders: List[TradeOrder] = field(default_factory=list)  # Список ордеров сетки (для grid_open)
    move_stop_to_breakeven: bool = True  # Перемещать ли стоп-лосс на безубыток после первого TP
    position_lifetime_seconds: int = 0  # Время жизни позиции в секундах (0 = отключено)
    max_take_profit_percent: float = 0.0  # Максимальная прибыль в процентах (0 = без ограничений)

class BingXTradingBot:
    """Торговый бот для работы с BingX"""

    def __init__(self, api_key: str, api_secret: str, leverage: int = 10, testnet: bool = True, logger=None):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        self.logger = logger
        self.leverage = leverage  # Плечо из конфигурации

        # Инициализация ccxt клиента для BingX
        # Создаем конфигурацию для exchange, но не сам объект
        self.exchange_config = {
            'apiKey': api_key,
            'secret': api_secret,
            'sandbox': testnet,  # Тестовая среда
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future',  # Фьючерсы
            }
        }
        # Exchange может быть установлен извне (для общего использования) или создан в initialize()
        self.exchange = None

        self.active_trades: Dict[str, TradeInstance] = {}
        self._markets_cache = None  # Кэш для рынков

    async def initialize(self):
        """Инициализация торгового бота"""
        try:
            # Создаем exchange объект
            self.exchange = ccxt.bingx(self.exchange_config)
            
            # Инициализируем exchange
            await self.exchange.load_markets()
            
            # Проверяем подключение
            balance = await self._get_usdt_balance()
            if self.logger:
                await self.logger.success(f"✅ Торговый бот инициализирован. Баланс USDT: ${balance:.2f}")
            
            # Синхронизируем позиции с биржей при запуске
            untracked_positions = await self.sync_positions_with_exchange()
            if untracked_positions:
                if self.logger:
                    await self.logger.warning(f"При запуске обнаружено {len(untracked_positions)} позиций, открытых вручную")
            
            return True
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка инициализации торгового бота: {str(e)}", e)
            return False

    def _generate_trade_id(self) -> str:
        """Генерирует уникальный ID для сделки"""
        return str(uuid.uuid4())[:8]

    def _generate_log_filename(self, trade_id: str) -> str:
        """Генерирует имя файла лога с датой и временем"""
        now = datetime.now()
        timestamp = now.strftime("%Y-%m-%d--%H-%M")
        return f"{timestamp}__{trade_id}-log.md"

    async def _get_usdt_balance(self) -> float:
        """Получает текущий свободный баланс USDT"""
        try:
            balance = self.exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0.0)

            if self.logger:
                await self.logger.info(f"💰 Текущий свободный баланс USDT: {usdt_balance:.2f}")

            return float(usdt_balance)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка получения баланса USDT: {str(e)}", e)
            return 0.0

    async def _get_total_usdt_balance(self) -> tuple[float, float]:
        """Получает общий баланс фьючерсного счета USDT (свободный + в позициях)"""
        try:
            balance = self.exchange.fetch_balance()
            usdt_info = balance.get('USDT', {})

            free_balance = usdt_info.get('free', 0.0)
            used_balance = usdt_info.get('used', 0.0)
            total_balance = usdt_info.get('total', free_balance + used_balance)

            if self.logger:
                await self.logger.info(f"💰 Общий баланс фьючерсного счета USDT: {total_balance:.2f} (свободно: {free_balance:.2f}, в позициях: {used_balance:.2f})")

            return float(total_balance), float(free_balance)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка получения общего баланса USDT: {str(e)}", e)
            # Возвращаем свободный баланс как резерв
            free_balance = await self._get_usdt_balance()
            return free_balance, free_balance

    async def _calculate_position_size(self, signal: TradingSignal, max_volume_usd: float, symbol: str, portfolio_percent: Optional[float] = None, remaining_portfolio_limit: float = 100) -> tuple[float, Optional[float]]:
        """Вычисляет размер позиции в контрактах с учетом ограничений биржи

        Args:
            signal: Торговый сигнал
            max_volume_usd: Максимальный объем в USD (используется если portfolio_percent не задан)
            symbol: Торговый символ
            portfolio_percent: Процент от портфеля (0.0-100.0), если задан - приоритет над max_volume_usd
            remaining_portfolio_limit: Оставшийся лимит портфеля в процентах (после вычета уже открытых позиций)
        """
        # Используем entry_price_now если доступно, иначе entry_price
        price_for_calculation = signal.entry_price_now if signal.entry_price_now else signal.entry_price
        if not price_for_calculation:
            return 0.0, None

        try:
            # Определяем объем для позиции
            if self.logger:
                await self.logger.info(f"🔍 ОТЛАДКА _calculate_position_size: portfolio_percent={portfolio_percent}, max_volume_usd={max_volume_usd}")
            if portfolio_percent is not None and portfolio_percent > 0:
                # Получаем общий баланс фьючерсного счета USDT
                total_balance, free_balance = await self._get_total_usdt_balance()

                if total_balance <= 0:
                    if self.logger:
                        await self.logger.warning(f"⚠️ Недостаточный общий баланс USDT: {total_balance}")
                    return 0.0, None

                # Рассчитываем залог как процент от ОБЩЕГО баланса (это сумма, которую мы готовы потерять)
                # portfolio_percent уже в процентах (например, 0.25 = 0.25%), поэтому делим на 100
                margin_usd = total_balance * (portfolio_percent / 100.0)

                # Проверяем, не превышает ли залог оставшийся лимит портфеля
                max_allowed_margin = total_balance * (remaining_portfolio_limit / 100.0)
                if margin_usd > max_allowed_margin:
                    margin_usd = max_allowed_margin
                    actual_portfolio_percent = (margin_usd / total_balance) * 100.0
                    if self.logger:
                        await self.logger.warning(f"⚠️ Залог ограничен оставшимся лимитом портфеля: {margin_usd:.2f} USD ({actual_portfolio_percent:.1f}% вместо {portfolio_percent}%)")
                else:
                    actual_portfolio_percent = portfolio_percent

                # С учетом плеча, размер позиции будет в {leverage} раз больше залога
                volume_usd = margin_usd * self.leverage

                if self.logger:
                    await self.logger.info(f"💰 Общий баланс фьючерсного счета: {total_balance:.2f} USDT, оставшийся лимит: {remaining_portfolio_limit:.1f}%")
                    await self.logger.info(f"💰 Расчет размера позиции: {actual_portfolio_percent:.1f}% от {total_balance:.2f} USDT = {margin_usd:.2f} USD залог")
                    await self.logger.info(f"💰 С плечом {self.leverage}x: {margin_usd:.2f} USD × {self.leverage} = {volume_usd:.2f} USD размер позиции")
            else:
                # Используем фиксированный объем
                volume_usd = max_volume_usd
                actual_portfolio_percent = None

                if self.logger:
                    await self.logger.info(f"💰 Используется фиксированный объем: {volume_usd:.2f} USD")

            # Получаем информацию о рынке
            markets = await self._get_markets()
            market_info = markets.get(symbol, {})

            # Базовый расчет размера позиции = Объем в USD / Цена входа
            position_size = volume_usd / price_for_calculation

            # Получаем ограничения по размеру позиции
            precision = market_info.get('precision', {})
            amount_precision = precision.get('amount', 0.001)  # Минимальная точность

            limits = market_info.get('limits', {})
            amount_limits = limits.get('amount', {})
            min_amount = amount_limits.get('min', amount_precision)
            max_amount = amount_limits.get('max', float('inf'))

            # Округляем до нужной точности
            if amount_precision >= 1:
                # Целые числа
                position_size = round(position_size)
            else:
                # Дробные числа - округляем до минимального шага
                decimals = len(str(amount_precision).split('.')[-1]) if '.' in str(amount_precision) else 0
                position_size = round(position_size, decimals)

            # Проверяем минимальный размер с учетом TP ордеров
            if min_amount is not None and position_size < min_amount:
                # Если позиция меньше минимума, увеличиваем до минимума
                position_size = min_amount
                if self.logger:
                    await self.logger.info(f"⚠️ Размер позиции {symbol} увеличен до минимума: {position_size}")

            # Дополнительная проверка для TP ордеров (нужно 3 TP по min_amount каждый)
            if min_amount is not None:
                min_for_multiple_tp = min_amount * 3  # Для 3 TP ордеров
                if position_size < min_for_multiple_tp:
                    # Если позиция слишком мала для 3 TP, увеличиваем до минимума для 3 TP
                    position_size = min_for_multiple_tp
                    if self.logger:
                        await self.logger.info(f"⚠️ Размер позиции {symbol} увеличен для 3 TP: {position_size}")

            # Проверяем максимальный размер
            if max_amount is not None and position_size > max_amount:
                position_size = max_amount
                if self.logger:
                    await self.logger.info(f"⚠️ Размер позиции {symbol} уменьшен до максимума: {position_size}")

            return position_size, actual_portfolio_percent

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Ошибка при расчете размера позиции для {symbol}: {str(e)}")
            # Возвращаем базовый расчет с фиксированным объемом
            return max_volume_usd / price_for_calculation, None

    def _calculate_take_profits(self, signal: TradingSignal, open_mode: OpenMode = OpenMode.DEFAULT, max_take_profit_percent: float = 0.0) -> List[float]:
        """Вычисляет уровни тейк профитов"""
        if not signal.entry_price:
            return []

        # Если в сигнале есть тейки, используем их
        if signal.take_profits and len(signal.take_profits) > 0:
            tp_levels = signal.take_profits[:3]  # Максимум 3 тейка
        elif open_mode == OpenMode.LIMIT_OPEN_1_LIMIT_EXIT_5:
            # Для режима LIMIT_OPEN_1_LIMIT_EXIT_5 используем один тейк профит 5%
            entry = signal.entry_price
            if signal.direction == SignalDirection.LONG:
                # Для лонга тейк выше цены входа
                tp = entry * 1.05  # +5%
            else:
                # Для шорта тейк ниже цены входа
                tp = entry * 0.95  # -5%
            tp_levels = [tp]
        else:
            # Иначе создаем автоматические тейки
            entry = signal.entry_price
            if signal.direction == SignalDirection.LONG:
                # Для лонга тейки выше цены входа
                tp1 = entry * 1.01  # +1%
                tp2 = entry * 1.02  # +2%
                tp3 = entry * 1.03  # +3%
            else:
                # Для шорта тейки ниже цены входа
                tp1 = entry * 0.99  # -1%
                tp2 = entry * 0.98  # -2%
                tp3 = entry * 0.97  # -3%
            tp_levels = [tp1, tp2, tp3]

        # Применяем ограничение max_take_profit_percent, если оно задано (больше 0)
        if max_take_profit_percent > 0:
            tp_levels = self._apply_max_profit_limit(tp_levels, signal.entry_price, signal.direction, max_take_profit_percent)

        return tp_levels

    def _apply_max_profit_limit(self, tp_levels: List[float], entry_price: float, direction: SignalDirection, max_take_profit_percent: float) -> List[float]:
        """Применяет ограничение максимальной прибыли к уровням тейк-профитов"""
        if not tp_levels or not entry_price or max_take_profit_percent <= 0:
            return tp_levels

        original_count = len(tp_levels)
        
        # Рассчитываем максимальную цену исходя из max_take_profit_percent в процентах
        if direction == SignalDirection.LONG:
            # Для лонга: максимальная цена = entry_price * (1 + max_take_profit_percent/100)
            max_price = entry_price * (1 + max_take_profit_percent / 100)
            # Заменяем тейки, которые превышают максимальную прибыль, на максимальное значение
            limited_tp_levels = [min(tp, max_price) for tp in tp_levels]
        else:  # SHORT
            # Для шорта: минимальная цена = entry_price * (1 - max_take_profit_percent/100)
            min_price = entry_price * (1 - max_take_profit_percent / 100)
            # Заменяем тейки, которые превышают максимальную прибыль, на максимальное значение
            limited_tp_levels = [max(tp, min_price) for tp in tp_levels]

        # Если все тейки превышают лимит, создаем один тейк на уровне максимальной прибыли
        if not limited_tp_levels:
            if direction == SignalDirection.LONG:
                limited_tp_levels = [max_price]
            else:
                limited_tp_levels = [min_price]

        # Логируем изменения (если есть логгер, но здесь это метод класса, без доступа к trade_id)
        # Логирование будет в _create_take_profits

        return limited_tp_levels

    async def _optimize_take_profit_levels(self, tp_levels: List[float], signal: TradingSignal, symbol: str) -> List[float]:
        """Оптимизирует take profit уровни для торговли по публичным сигналам"""
        try:
            # Получаем tick size для данной пары, передавая сигнал для анализа
            tick_size = await self._get_tick_size(symbol, signal)
            
            # Оптимизируем каждый TP уровень
            optimized_levels = []
            optimization_step = 2 * tick_size  # 2 тика
            
            for tp_price in tp_levels:
                if signal.direction == SignalDirection.LONG:
                    # Для лонга уменьшаем TP (встаем раньше в очереди на продажу)
                    optimized_price = tp_price - optimization_step
                else:
                    # Для шорта увеличиваем TP (встаем раньше в очереди на покупку)
                    optimized_price = tp_price + optimization_step
                
                # Округляем до tick size
                optimized_price = round(optimized_price / tick_size) * tick_size
                optimized_levels.append(optimized_price)
            
            if self.logger:
                await self.logger.info(f"🎯 Оптимизация TP для {symbol} (tick_size={tick_size}):")
                for i, (orig, opt) in enumerate(zip(tp_levels, optimized_levels), 1):
                    direction_symbol = "↓" if signal.direction == SignalDirection.LONG else "↑"
                    await self.logger.info(f"   TP{i}: {orig} {direction_symbol} {opt} (Δ={optimization_step})")
            
            return optimized_levels
            
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"⚠️ Ошибка оптимизации TP для {symbol}: {e}")
            # Возвращаем исходные уровни при ошибке
            return tp_levels

    def _calculate_stop_loss(self, signal: TradingSignal) -> float:
        """Вычисляет уровень стоп лосса"""
        if not signal.entry_price:
            return 0.0

        # Если в сигнале есть стоп, используем его
        if signal.stop_loss:
            return signal.stop_loss

        # Иначе создаем автоматический стоп
        entry = signal.entry_price
        if signal.direction == SignalDirection.LONG:
            # Для лонга стоп ниже цены входа
            stop = entry * 0.95  # -5%
        else:
            # Для шорта стоп выше цены входа
            stop = entry * 1.05  # +5%

        return stop

    async def _log_to_file(self, trade_id: str, message: str):
        """Логирует сообщение в файл сделки"""
        try:
            os.makedirs("./bots", exist_ok=True)
            log_filename = self._generate_log_filename(trade_id)
            log_file = f"./bots/{log_filename}"

            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"**{timestamp}** - {message}\n\n"

            with open(log_file, "a", encoding="utf-8") as f:
                f.write(log_entry)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка записи в лог файл для сделки {trade_id}: {str(e)}", e)

    async def _log_to_chat(self, message: str):
        """Логирует сообщение в целевой чат"""
        if self.logger and hasattr(self.logger, 'send_to_target_chat'):
            await self.logger.send_to_target_chat(f"🤖 **ТОРГОВЫЙ БОТ**: {message}")
        elif self.logger:
            await self.logger.info(f"🤖 **ТОРГОВЫЙ БОТ**: {message}")

    async def _log_trade_info_to_chat(self, trade: 'TradeInstance', total_balance: float, free_balance: float):
        """Логирует подробную информацию о сделке и балансе в чат"""
        try:
            # Формируем сообщение с информацией о сделке
            signal = trade.signal
            entry_order = trade.entry_order

            message = f"📊 **НОВАЯ СДЕЛКА ОТКРЫТА**\n\n"
            message += f"**ID сделки:** `{trade.id}`\n"
            message += f"**Канал:** {trade.source_channel}\n"
            message += f"**Сигнал:** {signal.direction.value.upper()} {signal.ticker}\n"
            message += f"**Цена входа:** ${signal.entry_price}\n"

            if entry_order:
                message += f"**Размер позиции:** {entry_order.amount}\n"
                message += f"**Номинал позиции:** ${trade.actual_volume_usd:.2f}\n"
                message += f"**Плечо:** {self.leverage}x\n"
                message += f"**ID ордера:** `{entry_order.exchange_order_id}`\n"

            # Информация о балансе
            message += f"\n💰 **БАЛАНС СЧЕТА**\n"
            message += f"**Общий баланс фьючерсного счета:** ${total_balance:.2f}\n"
            message += f"**Свободный USDT:** ${free_balance:.2f}\n"

            # Информация о процентах (если используется)
            if hasattr(trade, 'portfolio_percent_used') and trade.portfolio_percent_used:
                max_usage = trade.max_portfolio_usage
                available_balance = total_balance * (max_usage / 100.0)
                reserve_balance = total_balance - available_balance

                # Используем фактический процент для расчета залога
                actual_percent = getattr(trade, 'actual_portfolio_percent_used', trade.portfolio_percent_used)
                margin_used = total_balance * (actual_percent / 100.0)

                message += f"**Использовано:** {actual_percent:.1f}% от общего баланса (${margin_used:.2f} залог)\n"
                if max_usage < 100:
                    message += f"**Доступно для торговли:** ${available_balance:.2f} ({max_usage}%)\n"
                    message += f"**В резерве:** ${reserve_balance:.2f} ({100-max_usage}%)\n"

            # Информация о общем использовании портфеля всеми позициями (реальные данные с биржи)
            total_portfolio_volume, total_portfolio_percent, active_positions_count = await self._calculate_total_portfolio_usage(total_balance)
            if total_portfolio_volume > 0:
                message += f"\n📊 **ОБЩЕЕ ИСПОЛЬЗОВАНИЕ ПОРТФЕЛЯ**\n"
                message += f"**Всего задействовано позициями:** ${total_portfolio_volume:.2f} ({total_portfolio_percent:.1f}%)\n"
                message += f"**Количество активных позиций:** {active_positions_count}\n"

            # Информация об установленных ордерах
            message += f"\n📋 **УСТАНОВЛЕННЫЕ ОРДЕРА**\n"

            # Стоп-лосс
            if trade.stop_loss_order:
                sl_status = "✅ Установлен" if trade.stop_loss_order.exchange_order_id else "⚠️ Локально"
                message += f"🛡️ **Стоп-лосс:** {sl_status} @ ${trade.stop_loss_order.price}\n"
                if trade.stop_loss_order.exchange_order_id:
                    message += f"   ID: `{trade.stop_loss_order.exchange_order_id}`\n"
            else:
                message += f"🛡️ **Стоп-лосс:** ❌ Не установлен\n"

            # Тейк-профиты
            tp_count = len(trade.take_profit_orders)
            if tp_count > 0:
                message += f"💰 **Тейк-профиты ({tp_count}):**\n"
                for i, tp_order in enumerate(trade.take_profit_orders, 1):
                    tp_status = "✅" if tp_order.exchange_order_id else "⚠️"
                    message += f"   TP{i}: {tp_status} @ ${tp_order.price}\n"
                    if tp_order.exchange_order_id:
                        message += f"   ID: `{tp_order.exchange_order_id}`\n"
            else:
                message += f"💰 **Тейк-профиты:** ❌ Не установлены\n"

            # Отправляем сообщение
            await self._log_to_chat(message)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка логирования информации о сделке в чат: {str(e)}", e)

    async def get_available_symbols(self) -> List[str]:
        """Возвращает список всех доступных символов на BingX"""
        try:
            if not self.exchange:
                return []

            markets = await self._get_markets()
            symbols = list(markets.keys())

            # Фильтруем только USDT пары
            usdt_symbols = [symbol for symbol in symbols if 'USDT' in symbol]
            return sorted(usdt_symbols)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка получения доступных символов: {str(e)}", e)
            return []

    async def _get_markets(self):
        """Получает рынки с кэшированием"""
        if self._markets_cache is None:
            self._markets_cache = self.exchange.load_markets()
        return self._markets_cache

    async def _get_tick_size(self, symbol: str, signal: Optional[TradingSignal] = None) -> float:
        """Получает минимальный шаг цены (tick size) для торговой пары"""
        try:
            # Сначала пытаемся определить tick_size из сигнала (самый точный способ)
            if signal:
                tick_size = self._calculate_tick_size_from_signal(signal)
                if tick_size > 0:
                    if self.logger:
                        await self.logger.info(f"📏 Tick size для {symbol} определен из сигнала: {tick_size}")
                    return tick_size
            
            # Если не удалось из сигнала, пытаемся получить с биржи
            markets = await self._get_markets()
            market_info = markets.get(symbol, {})
            
            # Получаем точность цены из precision
            precision = market_info.get('precision', {})
            price_precision = precision.get('price')
            
            # Получаем tick size из limits
            limits = market_info.get('limits', {})
            price_limits = limits.get('price', {})
            tick_size = price_limits.get('min', price_precision)
            
            # Проверяем, что tick_size корректный
            if tick_size is not None and tick_size > 0:
                if self.logger:
                    await self.logger.info(f"📏 Tick size для {symbol} получен с биржи: {tick_size}")
                return tick_size
            
            # Если биржа не предоставила данные, возвращаем ошибку
            if self.logger:
                await self.logger.error(f"❌ Не удалось определить tick size для {symbol} ни из сигнала, ни с биржи")
            raise ValueError(f"Не удалось определить tick size для {symbol}")
            
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"⚠️ Ошибка получения tick size для {symbol}: {e}")
            raise

    def _calculate_tick_size_from_signal(self, signal: TradingSignal) -> float:
        """Определяет tick_size анализируя точность цен в сигнале"""
        prices = []
        
        # Собираем все цены из сигнала
        if signal.entry_price:
            prices.append(signal.entry_price)
        if signal.entry_price_now:
            prices.append(signal.entry_price_now)
        if signal.stop_loss:
            prices.append(signal.stop_loss)
        if signal.take_profits:
            prices.extend(signal.take_profits)
        
        if not prices:
            return 0.0
        
        # Определяем минимальный шаг из всех цен
        min_step = float('inf')
        
        for price in prices:
            # Преобразуем цену в строку для анализа точности
            price_str = f"{price:.10f}".rstrip('0').rstrip('.')
            
            if '.' in price_str:
                # Считаем количество знаков после запятой
                decimal_places = len(price_str.split('.')[1])
                step = 10 ** (-decimal_places)
                min_step = min(min_step, step)
        
        # Если все цены целые, возвращаем 1
        if min_step == float('inf'):
            return 1.0
        
        return min_step

    async def _normalize_symbol(self, ticker: str) -> Optional[str]:
        """Нормализует символ для использования с BingX (только фьючерсы/swap)"""
        try:
            # Получаем все доступные рынки
            markets = await self._get_markets()

            # Пробуем найти символ в разных форматах (ТОЛЬКО фьючерсы)
            possible_symbols = []

            # Если тикер уже в формате BASE/QUOTE
            if "/" in ticker:
                # Для фьючерсов добавляем :USDT постфикс
                if ticker.endswith("/USDT"):
                    possible_symbols.append(f"{ticker}:USDT")  # Фьючерсы - ПРИОРИТЕТ
                # Также пробуем исходный формат (может быть уже фьючерсным)
                possible_symbols.append(ticker)
            else:
                # Если тикер в формате BASEUSDT, преобразуем в BASE/USDT
                if ticker.upper().endswith("USDT"):
                    base = ticker[:-4].upper()
                    # ТОЛЬКО фьючерсы (swap)
                    possible_symbols.append(f"{base}/USDT:USDT")
                else:
                    # Добавляем USDT как котировочную валюту
                    base = ticker.upper()
                    # ТОЛЬКО фьючерсы (swap)
                    possible_symbols.append(f"{base}/USDT:USDT")

            # Проверяем, какой символ доступен на бирже (ТОЛЬКО фьючерсы/swap)
            for symbol in possible_symbols:
                if symbol in markets:
                    market_info = markets[symbol]
                    market_type = market_info.get('type', '').lower()

                    # Проверяем, что рынок активен И это фьючерсы/swap
                    if (market_info.get('active', True) and 
                        market_type in ['swap', 'future', 'derivative']):
                        return symbol

            # Если не нашли фьючерсы, логируем это как ошибку
            if self.logger:
                await self.logger.warning(f"Фьючерсный рынок для {ticker} не найден на BingX. API ключ должен иметь разрешения для фьючерсной торговли.")

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка нормализации символа {ticker}: {str(e)}", e)
            return None

    async def execute_signal(self, signal: TradingSignal, source_channel: str, max_volume_usd: float, portfolio_percent: Optional[float] = None, max_portfolio_usage: float = 100, open_mode: OpenMode = OpenMode.DEFAULT, move_stop_to_breakeven: bool = True, max_take_profit_percent: float = 0.0, position_lifetime: str = "0s") -> Optional[TradeInstance]:
        """Выполняет торговый сигнал"""
        try:
            # Проверяем, что exchange инициализирован
            if not self.exchange:
                if self.logger:
                    await self.logger.error("Exchange не инициализирован. Невозможно выполнить сигнал.")
                return None

            trade_id = self._generate_trade_id()

            # Парсим время жизни позиции из строки в секунды
            from utils import parse_duration
            position_lifetime_seconds = parse_duration(position_lifetime) if position_lifetime else 0

            # Создаем экземпляр сделки
            log_filename = self._generate_log_filename(trade_id)
            trade = TradeInstance(
                id=trade_id,
                signal=signal,
                source_channel=source_channel,
                entry_order=None,
                stop_loss_order=None,
                take_profit_orders=[],
                status=TradeStatus.ACTIVE,
                max_volume_usd=max_volume_usd,
                actual_volume_usd=0.0,
                max_portfolio_usage=max_portfolio_usage,
                open_mode=open_mode,
                move_stop_to_breakeven=move_stop_to_breakeven,
                max_take_profit_percent=max_take_profit_percent,
                position_lifetime_seconds=position_lifetime_seconds,
                created_at=datetime.now().isoformat(),
                log_file=f"./bots/{log_filename}"
            )

            await self._log_to_file(trade_id, f"# Торговая сделка {trade_id}")
            await self._log_to_file(trade_id, f"**Канал:** {source_channel}")
            await self._log_to_file(trade_id, f"**Сигнал:** {signal.direction} {signal.ticker} @ {signal.entry_price}")
            await self._log_to_file(trade_id, f"**Максимальный объем:** ${max_volume_usd}")
            if max_take_profit_percent > 0:
                await self._log_to_file(trade_id, f"**Макс. прибыль:** {max_take_profit_percent}% (тейк-профиты будут ограничены)")
            if position_lifetime_seconds > 0:
                from utils import format_duration
                await self._log_to_file(trade_id, f"**Время жизни позиции:** {format_duration(position_lifetime_seconds)} (автозакрытие)")

            # Нормализуем символ для биржи (только фьючерсы)
            symbol = await self._normalize_symbol(signal.ticker)
            if not symbol:
                await self._log_to_file(trade_id, f"❌ Фьючерсный символ {signal.ticker} не найден на бирже BingX")
                if self.logger:
                    await self.logger.warning(f"Фьючерсный символ {signal.ticker} не найден на BingX")
                return None

            # Проверяем тип рынка и логируем
            try:
                markets = await self._get_markets()
                market_info = markets.get(symbol, {})
                market_type = market_info.get('type', 'unknown')
                await self._log_to_file(trade_id, f"✅ Символ нормализован: {signal.ticker} -> {symbol} (тип: {market_type})")
            except Exception as e:
                await self._log_to_file(trade_id, f"✅ Символ нормализован: {signal.ticker} -> {symbol}")

            # Получаем текущий баланс для логирования
            current_total_balance, current_free_balance = await self._get_total_usdt_balance()
            await self._log_to_file(trade_id, f"💰 Общий баланс фьючерсного счета USDT: ${current_total_balance:.2f} (свободно: ${current_free_balance:.2f})")

            # Логируем информацию о общем использовании портфеля активными позициями (реальные данные с биржи)
            # ВАЖНО: НЕ исключаем текущую сделку, чтобы правильно учесть все существующие позиции
            total_portfolio_volume, total_portfolio_percent, active_positions_count = await self._calculate_total_portfolio_usage(current_total_balance)
            if total_portfolio_volume > 0:
                await self._log_to_file(trade_id, f"📊 Общее использование портфеля: ${total_portfolio_volume:.2f} ({total_portfolio_percent:.1f}%) - активных позиций: {active_positions_count}")
            else:
                await self._log_to_file(trade_id, f"📊 Общее использование портфеля: $0.00 (0.0%) - активных позиций: {active_positions_count}")

            # ПРОВЕРЯЕМ ЛИМИТ MAX_PORTFOLIO_USAGE С УЧЕТОМ УЖЕ ОТКРЫТЫХ ПОЗИЦИЙ
            if total_portfolio_percent >= max_portfolio_usage:
                await self._log_to_file(trade_id, f"🚫 СИГНАЛ ОТКЛОНЕН: Превышен лимит использования портфеля ({total_portfolio_percent:.1f}% >= {max_portfolio_usage}%)")
                if self.logger:
                    await self.logger.warning(f"🚫 Сигнал отклонен для {signal.ticker}: использование портфеля {total_portfolio_percent:.1f}% превышает лимит {max_portfolio_usage}%")
                
                # Отправляем уведомление в TARGET телеграм канал
                portfolio_warning_message = f"🚫 **СИГНАЛ ОТКЛОНЕН - ПРЕВЫШЕН ЛИМИТ ПОРТФЕЛЯ**\n\n"
                portfolio_warning_message += f"**Канал:** {source_channel}\n"
                portfolio_warning_message += f"**Сигнал:** {signal.direction.value.upper()} {signal.ticker}\n"
                portfolio_warning_message += f"**Цена входа:** ${signal.entry_price}\n\n"
                portfolio_warning_message += f"💰 **СОСТОЯНИЕ ПОРТФЕЛЯ**\n"
                portfolio_warning_message += f"**Общий баланс:** ${current_total_balance:.2f}\n"
                portfolio_warning_message += f"**Текущее использование:** {total_portfolio_percent:.1f}%\n"
                portfolio_warning_message += f"**Лимит MAX_PORTFOLIO_USAGE:** {max_portfolio_usage}%\n"
                portfolio_warning_message += f"**Активных позиций:** {active_positions_count}\n\n"
                portfolio_warning_message += f"⚠️ **Для размещения ордера требуется освободить портфель или увеличить лимит MAX_PORTFOLIO_USAGE в .env файле**"
                
                await self._log_to_chat(portfolio_warning_message)
                return None

            # Вычисляем максимально допустимый объем для новой позиции
            remaining_portfolio_limit = max_portfolio_usage - total_portfolio_percent
            max_allowed_new_volume_usd = current_total_balance * (remaining_portfolio_limit / 100.0)

            await self._log_to_file(trade_id, f"📏 Остается лимита портфеля: {remaining_portfolio_limit:.1f}% (${max_allowed_new_volume_usd:.2f})")

            # Вычисляем размер позиции с учетом ограничений биржи И остатка лимита портфеля
            await self._log_to_file(trade_id, f"🔍 ОТЛАДКА: portfolio_percent={portfolio_percent}, max_volume_usd={max_volume_usd}")
            position_size, actual_portfolio_percent = await self._calculate_position_size(signal, max_volume_usd, symbol, portfolio_percent, remaining_portfolio_limit)
            if position_size <= 0:
                await self._log_to_file(trade_id, "❌ Ошибка: невозможно вычислить размер позиции")
                return None

            # ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: Учитываем новую позицию в общем использовании
            new_position_margin = (position_size * signal.entry_price) / self.leverage
            new_total_usage_percent = ((total_portfolio_volume + new_position_margin) / current_total_balance) * 100
            
            if new_total_usage_percent > max_portfolio_usage:
                await self._log_to_file(trade_id, f"🚫 СИГНАЛ ОТКЛОНЕН: Новая позиция превысит лимит ({new_total_usage_percent:.1f}% > {max_portfolio_usage}%)")
                if self.logger:
                    await self.logger.warning(f"🚫 Сигнал отклонен для {signal.ticker}: новая позиция превысит лимит {new_total_usage_percent:.1f}% > {max_portfolio_usage}%")
                
                # Отправляем уведомление в TARGET телеграм канал
                portfolio_warning_message = f"🚫 **СИГНАЛ ОТКЛОНЕН - НОВАЯ ПОЗИЦИЯ ПРЕВЫСИТ ЛИМИТ**\n\n"
                portfolio_warning_message += f"**Канал:** {source_channel}\n"
                portfolio_warning_message += f"**Сигнал:** {signal.direction.value.upper()} {signal.ticker}\n"
                portfolio_warning_message += f"**Цена входа:** ${signal.entry_price}\n\n"
                portfolio_warning_message += f"💰 **СОСТОЯНИЕ ПОРТФЕЛЯ**\n"
                portfolio_warning_message += f"**Общий баланс:** ${current_total_balance:.2f}\n"
                portfolio_warning_message += f"**Текущее использование:** {total_portfolio_percent:.1f}%\n"
                portfolio_warning_message += f"**Использование с новой позицией:** {new_total_usage_percent:.1f}%\n"
                portfolio_warning_message += f"**Лимит MAX_PORTFOLIO_USAGE:** {max_portfolio_usage}%\n"
                portfolio_warning_message += f"**Активных позиций:** {active_positions_count}\n\n"
                portfolio_warning_message += f"⚠️ **Для размещения ордера требуется освободить портфель или увеличить лимит MAX_PORTFOLIO_USAGE в .env файле**"
                
                await self._log_to_chat(portfolio_warning_message)
                return None

            # Сохраняем информацию о проценте портфеля, если он использовался
            if portfolio_percent is not None and current_total_balance > 0:
                trade.portfolio_percent_used = portfolio_percent
                trade.actual_portfolio_percent_used = actual_portfolio_percent
                await self._log_to_file(trade_id, f"📏 Размер позиции: {position_size} ({actual_portfolio_percent:.1f}% от портфеля)")
            else:
                await self._log_to_file(trade_id, f"📏 Размер позиции: {position_size} (фиксированная сумма)")

            # Устанавливаем плечо
            try:
                # Определяем side для BingX API на основе направления сигнала
                side = 'LONG' if signal.direction == SignalDirection.LONG else 'SHORT'

                # CCXT методы синхронные
                self.exchange.set_leverage(self.leverage, symbol, {'side': side})
                await self._log_to_file(trade_id, f"✅ Плечо установлено: {self.leverage}x для {side}")
            except Exception as e:
                error_msg = str(e)
                await self._log_to_file(trade_id, f"❌ Не удалось установить плечо: {error_msg}")
                if self.logger:
                    await self.logger.error(f"Ошибка установки плеча {self.leverage}x для {symbol}: {error_msg}", e)
                # Если не удалось установить плечо, прерываем выполнение сигнала
                return None

            # Открываем позицию в зависимости от режима
            if open_mode == OpenMode.GRID_OPEN:
                await self._create_grid_orders(trade, symbol, position_size)
            elif open_mode == OpenMode.LIMIT_OPEN_1_LIMIT_EXIT_5:
                await self._create_limit_entry_order_1_percent(trade, symbol, position_size)
            else:
                await self._create_market_entry_order(trade, symbol, position_size)

            # Создаем стоп лосс
            await self._log_to_file(trade_id, "🛡️ Создание стоп лосса...")
            await self._create_stop_loss(trade)

            # Создаем тейк профиты
            await self._log_to_file(trade_id, "💰 Создание тейк профитов...")
            await self._create_take_profits(trade)

            # Итоговая информация
            sl_status = "✅ Установлен" if trade.stop_loss_order else "❌ Ошибка"
            tp_count = len(trade.take_profit_orders)
            await self._log_to_file(trade_id, f"📋 Итог: Стоп лосс - {sl_status}, Тейк профитов - {tp_count}/3")

            # Логируем подробную информацию о сделке в чат
            await self._log_trade_info_to_chat(trade, current_total_balance, current_free_balance)

            # Добавляем в активные сделки
            self.active_trades[trade_id] = trade

            return trade

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка выполнения сигнала: {str(e)}", e)
            await self._log_to_file(trade_id, f"❌ Критическая ошибка: {str(e)}")
            return None

    async def _create_stop_loss(self, trade: TradeInstance):
        """Создает стоп лосс ордер"""
        try:
            stop_price = self._calculate_stop_loss(trade.signal)
            if stop_price <= 0:
                await self._log_to_file(trade.id, "⚠️ Стоп лосс не установлен: некорректная цена")
                return

            # Определяем сторону для стоп лосса (противоположную входу)
            side = 'sell' if trade.signal.direction == SignalDirection.LONG else 'buy'

            stop_order = TradeOrder(
                id=f"{trade.id}_stop",
                symbol=trade.entry_order.symbol,
                side=side,
                amount=trade.entry_order.filled_amount,
                price=stop_price,
                order_type=OrderType.STOP_MARKET,
                status=OrderStatus.OPEN,
                created_at=datetime.now().isoformat()
            )

            # Подготавливаем параметры для BingX hedge mode
            order_params = {
                'positionSide': 'LONG' if trade.signal.direction == SignalDirection.LONG else 'SHORT',
                'stopPrice': stop_price,
                'workingType': 'MARK_PRICE',  # Используем mark price для триггера
                'timeInForce': 'GTC'  # Good Till Cancelled
            }

            # Размещаем стоп ордер на бирже BingX
            try:
                # Для BingX используем STOP_MARKET тип
                order_result = self.exchange.create_order(
                    trade.entry_order.symbol,
                    'STOP_MARKET',  # Правильный тип для стоп лосса
                    side,
                    trade.entry_order.filled_amount,
                    None,  # price = None для market order
                    order_params
                )

                stop_order.exchange_order_id = order_result['id']
                trade.stop_loss_order = stop_order

                await self._log_to_file(trade.id, f"🛡️ Стоп лосс размещен на бирже: {side.upper()} @ {stop_price} (ID: {order_result['id']})")

            except Exception as e:
                # Пробуем альтернативный подход - обычный стоп ордер
                try:
                    order_params_alt = {
                        'positionSide': 'LONG' if trade.signal.direction == SignalDirection.LONG else 'SHORT',
                        'stopPrice': stop_price,
                        'timeInForce': 'GTC'
                    }

                    order_result = self.exchange.create_order(
                        trade.entry_order.symbol,
                        'stop',  # Альтернативный тип
                        side,
                        trade.entry_order.filled_amount,
                        None,
                        order_params_alt
                    )

                    stop_order.exchange_order_id = order_result['id']
                    trade.stop_loss_order = stop_order

                    await self._log_to_file(trade.id, f"🛡️ Стоп лосс размещен на бирже (альт): {side.upper()} @ {stop_price} (ID: {order_result['id']})")

                except Exception as e2:
                    # Если не удалось разместить на бирже, сохраняем локально
                    trade.stop_loss_order = stop_order
                    await self._log_to_file(trade.id, f"⚠️ Стоп лосс сохранен локально (ошибка биржи): {side.upper()} @ {stop_price} - {str(e)} | {str(e2)}")

        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка создания стоп лосса: {str(e)}")

    async def _create_market_entry_order(self, trade: TradeInstance, symbol: str, position_size: float):
        """Создает рыночный входной ордер"""
        try:
            signal = trade.signal
            side = 'buy' if signal.direction == SignalDirection.LONG else 'sell'

            entry_order = TradeOrder(
                id=f"{trade.id}_entry",
                symbol=symbol,
                side=side,
                amount=position_size,
                price=signal.entry_price,
                order_type=OrderType.MARKET,  # Рыночный ордер для входа
                status=OrderStatus.OPEN,
                created_at=datetime.now().isoformat()
            )

            # Подготавливаем параметры для BingX hedge mode
            order_params = {
                'positionSide': 'LONG' if signal.direction == SignalDirection.LONG else 'SHORT'
            }

            await self._log_to_file(trade.id, f"🎯 Hedge mode: positionSide = {order_params['positionSide']}")

            # Размещаем входной ордер с параметрами hedge mode
            # CCXT методы синхронные
            order_result = self.exchange.create_order(symbol, 'market', side, position_size, None, order_params)
            entry_order.exchange_order_id = order_result['id']
            entry_order.filled_amount = order_result.get('filled', 0)

            if order_result.get('status') == 'closed':
                entry_order.status = OrderStatus.CLOSED
                entry_order.filled_at = datetime.now().isoformat()
                trade.entry_price = order_result.get('average', signal.entry_price)
                trade.actual_volume_usd = trade.entry_price * entry_order.filled_amount

            trade.entry_order = entry_order

            await self._log_to_file(trade.id, f"📊 Входной ордер размещен: {side.upper()} {position_size} {symbol} @ {signal.entry_price}")

        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка создания рыночного ордера: {str(e)}")

    async def _create_limit_entry_order_1_percent(self, trade: TradeInstance, symbol: str, position_size: float):
        """Создает лимитный входной ордер используя entry_price_now для лимитной цены"""
        try:
            signal = trade.signal
            side = 'buy' if signal.direction == SignalDirection.LONG else 'sell'

            # Используем entry_price_now если доступно, иначе entry_price
            base_price = signal.entry_price_now if signal.entry_price_now else signal.entry_price
            limit_price = base_price  # Используем текущую рыночную цену как лимитную

            entry_order = TradeOrder(
                id=f"{trade.id}_entry",
                symbol=symbol,
                side=side,
                amount=position_size,
                price=limit_price,
                order_type=OrderType.LIMIT,  # Лимитный ордер для входа
                status=OrderStatus.OPEN,
                created_at=datetime.now().isoformat()
            )

            # Подготавливаем параметры для BingX hedge mode
            order_params = {
                'positionSide': 'LONG' if signal.direction == SignalDirection.LONG else 'SHORT',
                'timeInForce': 'GTC'  # Good Till Cancelled
            }

            await self._log_to_file(trade.id, f"🎯 Hedge mode: positionSide = {order_params['positionSide']}")

            # Размещаем лимитный ордер с параметрами hedge mode
            order_result = self.exchange.create_order(
                symbol, 
                'limit', 
                side, 
                position_size, 
                limit_price, 
                order_params
            )

            entry_order.exchange_order_id = order_result['id']

            # Если ордер сразу исполнился (маловероятно для лимитного)
            if order_result.get('status') == 'closed':
                entry_order.status = OrderStatus.CLOSED
                entry_order.filled_at = datetime.now().isoformat()
                entry_order.filled_amount = order_result.get('filled', 0)
                trade.entry_price = order_result.get('average', limit_price)
                trade.actual_volume_usd = trade.entry_price * entry_order.filled_amount

            trade.entry_order = entry_order

            await self._log_to_file(trade.id, f"📊 Лимитный входной ордер размещен: {side.upper()} {position_size} {symbol} @ {limit_price} (текущая рыночная цена)")

        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка создания лимитного ордера: {str(e)}")

    async def _create_grid_orders(self, trade: TradeInstance, symbol: str, total_position_size: float):
        """Создает сетку лимитных ордеров для входа"""
        try:
            signal = trade.signal
            side = 'buy' if signal.direction == SignalDirection.LONG else 'sell'

            # Разбиваем общий размер позиции на 10 ордеров
            order_size = total_position_size / 10

            # Округляем до минимального размера
            markets = await self._get_markets()
            market_info = markets.get(symbol, {})
            limits = market_info.get('limits', {})
            amount_limits = limits.get('amount', {})
            min_amount = amount_limits.get('min', 0.001)

            if order_size < min_amount:
                order_size = min_amount
                await self._log_to_file(trade.id, f"⚠️ Размер ордера сетки увеличен до минимума: {order_size}")

            # Рассчитываем цены для сетки (шаг 1%)
            # Используем entry_price_now если доступно, иначе entry_price
            base_price = signal.entry_price_now if signal.entry_price_now else signal.entry_price

            for i in range(10):
                if signal.direction == SignalDirection.LONG:
                    # Для лонга: первый ордер по рыночной цене, следующие дешевле на 1% каждый
                    order_price = base_price * (1 - i * 0.01)
                else:
                    # Для шорта: первый ордер по рыночной цене, следующие дороже на 1% каждый  
                    order_price = base_price * (1 + i * 0.01)

                grid_order = TradeOrder(
                    id=f"{trade.id}_grid_{i+1}",
                    symbol=symbol,
                    side=side,
                    amount=order_size,
                    price=order_price,
                    order_type=OrderType.LIMIT,  # Лимитный ордер
                    status=OrderStatus.OPEN,
                    created_at=datetime.now().isoformat()
                )

                # Подготавливаем параметры для BingX hedge mode
                order_params = {
                    'positionSide': 'LONG' if signal.direction == SignalDirection.LONG else 'SHORT',
                    'timeInForce': 'GTC'  # Good Till Cancelled
                }

                try:
                    # Размещаем лимитный ордер
                    order_result = self.exchange.create_order(
                        symbol, 
                        'limit', 
                        side, 
                        order_size, 
                        order_price, 
                        order_params
                    )

                    grid_order.exchange_order_id = order_result['id']
                    await self._log_to_file(trade.id, f"📊 Ордер сетки {i+1}/10: {side.upper()} {order_size} @ ${order_price:.4f} (ID: {order_result['id']})")

                except Exception as e:
                    await self._log_to_file(trade.id, f"⚠️ Ошибка размещения ордера сетки {i+1}: {str(e)}")

                trade.grid_orders.append(grid_order)

            # Устанавливаем первый ордер как основной входной ордер
            if trade.grid_orders:
                trade.entry_order = trade.grid_orders[0]
                await self._log_to_file(trade.id, f"📊 Создана сетка из {len(trade.grid_orders)} ордеров")

        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка создания сетки ордеров: {str(e)}")

    async def _create_take_profits(self, trade: TradeInstance):
        """Создает тейк профит ордера"""
        try:
            # Получаем оригинальные TP перед применением ограничений
            original_tp_levels = self._calculate_take_profits(trade.signal, trade.open_mode, 0.0)  # Без ограничений
            tp_levels = self._calculate_take_profits(trade.signal, trade.open_mode, trade.max_take_profit_percent)
            
            if not tp_levels:
                await self._log_to_file(trade.id, "⚠️ Тейк профиты не установлены: некорректные уровни")
                return

            # Логируем применение ограничения max_take_profit_percent
            if trade.max_take_profit_percent > 0 and len(original_tp_levels) != len(tp_levels):
                await self._log_to_file(trade.id, f"📉 Применено ограничение max_take_profit_percent {trade.max_take_profit_percent}%: {len(original_tp_levels)} → {len(tp_levels)} тейк-профитов")

            # Оптимизируем TP уровни для публичных сигналов (уменьшаем на 2 тика)
            tp_levels = await self._optimize_take_profit_levels(tp_levels, trade.signal, trade.entry_order.symbol)
            await self._log_to_file(trade.id, f"🎯 TP уровни оптимизированы для публичных сигналов")

            # Определяем сторону для тейк профитов (противоположную входу)
            side = 'sell' if trade.signal.direction == SignalDirection.LONG else 'buy'

            # Получаем минимальный размер ордера для символа
            markets = await self._get_markets()
            market_info = markets.get(trade.entry_order.symbol, {})
            limits = market_info.get('limits', {})
            amount_limits = limits.get('amount', {})
            min_amount = amount_limits.get('min', 0.001)

            # Разделяем позицию на равные части для каждого тейка
            tp_amount = trade.entry_order.filled_amount / len(tp_levels)

            # Проверяем, что размер каждого TP больше минимального
            if min_amount and tp_amount < min_amount:
                # Если размер слишком мал, создаем один TP на всю позицию
                await self._log_to_file(trade.id, f"⚠️ Размер TP {tp_amount} < минимум {min_amount}, создаем один TP на всю позицию")
                tp_levels = [tp_levels[0]]  # Берем только первый уровень
                tp_amount = trade.entry_order.filled_amount

            for i, tp_price in enumerate(tp_levels):
                tp_order = TradeOrder(
                    id=f"{trade.id}_tp{i+1}",
                    symbol=trade.entry_order.symbol,
                    side=side,
                    amount=tp_amount,
                    price=tp_price,
                    order_type=OrderType.TAKE_PROFIT_MARKET,
                    status=OrderStatus.OPEN,
                    created_at=datetime.now().isoformat()
                )

                # Подготавливаем параметры для BingX hedge mode
                order_params = {
                    'positionSide': 'LONG' if trade.signal.direction == SignalDirection.LONG else 'SHORT',
                    'stopPrice': tp_price,
                    'workingType': 'MARK_PRICE',  # Используем mark price для триггера
                    'timeInForce': 'GTC'  # Good Till Cancelled
                }

                # Размещаем тейк профит ордер на бирже BingX
                try:
                    order_result = self.exchange.create_order(
                        trade.entry_order.symbol,
                        'TAKE_PROFIT_MARKET',  # Правильный тип для take profit
                        side,
                        tp_amount,
                        None,  # price = None для market order
                        order_params
                    )

                    tp_order.exchange_order_id = order_result['id']
                    await self._log_to_file(trade.id, f"💰 Тейк профит {i+1} размещен на бирже: {side.upper()} @ {tp_price} (ID: {order_result['id']})")

                except Exception as e:
                    # Пробуем альтернативный подход
                    try:
                        order_params_alt = {
                            'positionSide': 'LONG' if trade.signal.direction == SignalDirection.LONG else 'SHORT',
                            'stopPrice': tp_price,
                            'timeInForce': 'GTC'
                        }

                        order_result = self.exchange.create_order(
                            trade.entry_order.symbol,
                            'takeProfit',  # Альтернативный тип
                            side,
                            tp_amount,
                            None,
                            order_params_alt
                        )

                        tp_order.exchange_order_id = order_result['id']
                        await self._log_to_file(trade.id, f"💰 Тейк профит {i+1} размещен на бирже (альт): {side.upper()} @ {tp_price} (ID: {order_result['id']})")

                    except Exception as e2:
                        # Если не удалось разместить на бирже, сохраняем локально
                        await self._log_to_file(trade.id, f"⚠️ Тейк профит {i+1} сохранен локально (ошибка биржи): {side.upper()} @ {tp_price} - {str(e)} | {str(e2)}")

                trade.take_profit_orders.append(tp_order)

        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка создания тейк профитов: {str(e)}")

    async def _move_stop_to_breakeven(self, trade: TradeInstance):
        """Перемещает стоп лосс на безубыток с отступом в один тик"""
        try:
            if trade.breakeven_moved or not trade.stop_loss_order or not trade.entry_price:
                return

            # Сначала отменяем старый стоп ордер
            if trade.stop_loss_order.exchange_order_id:
                try:
                    self.exchange.cancel_order(trade.stop_loss_order.exchange_order_id, trade.stop_loss_order.symbol)
                    await self._log_to_file(trade.id, f"🗑️ Старый стоп лосс отменен")
                except Exception as e:
                    await self._log_to_file(trade.id, f"⚠️ Ошибка отмены старого стопа: {str(e)}")

            # КРИТИЧНО: Получаем актуальный размер позиции с биржи
            # После исполнения TP размер позиции уменьшился
            is_position_open, actual_position_size = await self._verify_position_still_open(trade)

            if not is_position_open:
                await self._log_to_file(trade.id, f"⚠️ Позиция уже закрыта на бирже - отменяем перемещение стоп-лосса")
                return

            # Используем актуальный размер позиции для стоп-лосса
            stop_loss_size = actual_position_size
            original_size = trade.entry_order.filled_amount or 0

            if abs(stop_loss_size - original_size) > 0.001:
                await self._log_to_file(trade.id, f"📊 Актуальный размер позиции: {stop_loss_size} (было {original_size})")
            else:
                await self._log_to_file(trade.id, f"📊 Размер позиции не изменился: {stop_loss_size}")

            # Получаем tick_size для точного расчета минимального шага
            try:
                tick_size = await self._get_tick_size(trade.stop_loss_order.symbol, trade.signal)
            except Exception as e:
                # Fallback на базовый tick_size если не удалось получить
                tick_size = 0.01
                await self._log_to_file(trade.id, f"⚠️ Не удалось получить tick_size, используем fallback {tick_size}: {str(e)}")

            # Рассчитываем цену стоп-лосса с отступом в 1 тик от цены входа
            # Это обеспечивает минимальный убыток, но защищает от отклонения биржей
            if trade.signal.direction == SignalDirection.LONG:
                # Для лонга стоп на 1 тик ниже цены входа
                breakeven_stop_price = trade.entry_price - tick_size
            else:
                # Для шорта стоп на 1 тик выше цены входа
                breakeven_stop_price = trade.entry_price + tick_size

            # Округляем до tick_size для корректности
            breakeven_stop_price = round(breakeven_stop_price / tick_size) * tick_size

            side = 'sell' if trade.signal.direction == SignalDirection.LONG else 'buy'

            # Подготавливаем параметры для BingX hedge mode
            order_params = {
                'positionSide': 'LONG' if trade.signal.direction == SignalDirection.LONG else 'SHORT',
                'stopPrice': breakeven_stop_price,
                'workingType': 'MARK_PRICE',  # Используем mark price для триггера
                'timeInForce': 'GTC'  # Good Till Cancelled
            }

            try:
                # Размещаем новый стоп ордер на бирже с актуальным размером позиции
                order_result = self.exchange.create_order(
                    trade.stop_loss_order.symbol,
                    'STOP_MARKET',  # Правильный тип для стоп лосса
                    side,
                    stop_loss_size,  # Используем актуальный размер позиции
                    None,  # price = None для market order
                    order_params
                )

                # Обновляем информацию о стоп ордере
                trade.stop_loss_order.exchange_order_id = order_result['id']
                trade.stop_loss_order.price = breakeven_stop_price
                trade.stop_loss_order.amount = stop_loss_size  # Обновляем размер стоп-лосса
                trade.breakeven_moved = True

                # Рассчитываем разность в тиках для логирования
                tick_difference = abs((breakeven_stop_price - trade.entry_price) / tick_size)
                direction_text = "ниже" if trade.signal.direction == SignalDirection.LONG else "выше"

                await self._log_to_file(trade.id, f"🔄 Стоп лосс перемещен на безубыток: ${breakeven_stop_price:.8f} (1 тик {direction_text} входа, размер={stop_loss_size}, tick_size={tick_size}, ID: {order_result['id']})")
                await self._log_to_chat(f"🔄 Стоп лосс сделки {trade.id} перемещен на безубыток @ ${breakeven_stop_price:.8f} (-1 тик)")

            except Exception as e:
                # Если не удалось разместить на бирже, обновляем локально
                trade.stop_loss_order.price = breakeven_stop_price
                trade.stop_loss_order.amount = stop_loss_size  # Обновляем размер стоп-лосса
                trade.breakeven_moved = True

                # Рассчитываем разность в тиках для логирования
                tick_difference = abs((breakeven_stop_price - trade.entry_price) / tick_size)
                direction_text = "ниже" if trade.signal.direction == SignalDirection.LONG else "выше"

                await self._log_to_file(trade.id, f"🔄 Стоп лосс перемещен на безубыток локально: ${breakeven_stop_price:.8f} (1 тик {direction_text} входа, размер={stop_loss_size}, tick_size={tick_size}) - {str(e)}")
                await self._log_to_chat(f"🔄 Стоп лосс сделки {trade.id} перемещен на безубыток @ ${breakeven_stop_price:.8f} (-1 тик)")

        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка перемещения стопа на безубыток: {str(e)}")

    async def _check_position_lifetime(self, trade: TradeInstance):
        """Проверяет время жизни позиции и закрывает её при истечении"""
        try:
            if not trade.created_at or trade.status != TradeStatus.ACTIVE:
                return
                
            # Рассчитываем время жизни позиции
            created_time = datetime.fromisoformat(trade.created_at)
            current_time = datetime.now()
            position_age_seconds = (current_time - created_time).total_seconds()
            
            # Если время жизни истекло - проверяем, нужно ли закрывать
            if position_age_seconds >= trade.position_lifetime_seconds:
                from utils import format_duration
                age_formatted = format_duration(int(position_age_seconds))
                lifetime_formatted = format_duration(trade.position_lifetime_seconds)
                
                await self._log_to_file(trade.id, f"⏰ Время жизни позиции истекло: {age_formatted} >= {lifetime_formatted}")
                
                # КРИТИЧНО: Проверяем, что позиция действительно еще открыта на бирже и получаем актуальный размер
                is_position_open, actual_position_size = await self._verify_position_still_open(trade)
                
                if is_position_open:
                    await self._log_to_file(trade.id, f"✅ Позиция подтверждена открытой на бирже - закрываем принудительно")
                    await self._log_to_chat(f"⏰ Позиция {trade.id} закрыта по таймауту: {lifetime_formatted}")
                    
                    # Закрываем позицию принудительно с актуальным размером
                    await self._force_close_position(trade, actual_position_size)
                else:
                    await self._log_to_file(trade.id, f"ℹ️ Позиция уже закрыта на бирже - обновляем локальный статус")
                    await self._log_to_chat(f"ℹ️ Позиция {trade.id} уже была закрыта (время жизни: {lifetime_formatted})")
                    
                    # Позиция уже закрыта - просто обновляем статус локально
                    await self._update_trade_as_closed(trade, "Позиция уже закрыта на бирже")
                
        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка проверки времени жизни позиции: {str(e)}")

    async def _verify_position_still_open(self, trade: TradeInstance) -> tuple[bool, float]:
        """Проверяет, что позиция действительно еще открыта на бирже и возвращает актуальный размер"""
        try:
            if not trade.entry_order or not trade.entry_order.symbol:
                await self._log_to_file(trade.id, f"⚠️ Нет информации о символе для проверки позиции")
                return False, 0.0
            
            symbol = trade.entry_order.symbol
            
            # Получаем все открытые позиции с биржи
            positions = await self._get_open_positions_from_exchange()
            
            # Ищем нашу позицию среди открытых
            target_side = 'LONG' if trade.signal.direction == SignalDirection.LONG else 'SHORT'
            
            for position in positions:
                if not isinstance(position, dict):
                    continue
                
                pos_symbol = position.get('symbol', '')
                pos_side = position.get('side', '').upper()
                pos_size = position.get('contracts', 0) or position.get('size', 0) or 0
                
                # Проверяем совпадение символа, направления и наличие размера
                if (pos_symbol == symbol and 
                    pos_side == target_side and 
                    abs(pos_size) > 0):
                    
                    actual_size = abs(pos_size)
                    original_size = trade.entry_order.filled_amount or 0
                    
                    if abs(actual_size - original_size) > 0.001:  # Если размер изменился
                        await self._log_to_file(trade.id, f"🔍 Позиция найдена: {pos_symbol} {pos_side}")
                        await self._log_to_file(trade.id, f"📊 Размер изменился: {original_size} → {actual_size}")
                    else:
                        await self._log_to_file(trade.id, f"🔍 Позиция найдена: {pos_symbol} {pos_side} размер={actual_size}")
                    
                    return True, actual_size
            
            await self._log_to_file(trade.id, f"🔍 Позиция {symbol} {target_side} не найдена среди открытых на бирже")
            return False, 0.0
            
        except Exception as e:
            await self._log_to_file(trade.id, f"⚠️ Ошибка проверки позиции на бирже: {str(e)}")
            # При ошибке проверки считаем, что позиция открыта с исходным размером (консервативный подход)
            original_size = trade.entry_order.filled_amount if trade.entry_order else 0.0
            return True, original_size

    async def _update_trade_as_closed(self, trade: TradeInstance, reason: str):
        """Обновляет статус сделки как закрытой без действий на бирже"""
        try:
            # Обновляем статус сделки
            trade.status = TradeStatus.COMPLETED
            trade.completed_at = datetime.now().isoformat()
            
            # Пытаемся получить PnL из позиции на бирже если он еще не рассчитан
            if trade.realized_pnl == 0.0:
                try:
                    positions = await self._get_open_positions_from_exchange()
                    # Ищем соответствующую позицию для получения realized PnL
                    for position in positions:
                        if (position.get('symbol') == trade.entry_order.symbol and
                            position.get('side', '').upper() == ('LONG' if trade.signal.direction.value.upper() == 'LONG' else 'SHORT')):
                            # Если позиция закрыта, пытаемся получить realized PnL
                            trade.realized_pnl = position.get('realizedPnl', 0.0) or 0.0
                            break
                except:
                    pass  # Если не удалось получить PnL с биржи, оставляем 0
            
            # Обновляем статистику канала
            if trade.source_channel:
                is_win = trade.realized_pnl > 0
                await self._update_channel_stats(trade.source_channel, is_win)
            
            # Удаляем из активных сделок
            if trade.id in self.active_trades:
                del self.active_trades[trade.id]
            
            await self._log_to_file(trade.id, f"ℹ️ Сделка помечена как закрытая: {reason}")
            await self._log_trade_close_to_chat(trade, reason)
            
        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка обновления статуса сделки: {str(e)}")

    async def _force_close_position(self, trade: TradeInstance, actual_position_size: float = None):
        """Принудительно закрывает позицию рыночным ордером"""
        try:
            # Отменяем все открытые ордера
            await self._cancel_all_orders(trade)
            
            # Закрываем позицию рыночным ордером с актуальным размером
            await self._close_position_market_order(trade, actual_position_size)
            
            # Обновляем статус сделки
            trade.status = TradeStatus.COMPLETED
            trade.completed_at = datetime.now().isoformat()
            
            # Обновляем статистику канала
            if trade.source_channel:
                is_win = trade.realized_pnl > 0
                await self._update_channel_stats(trade.source_channel, is_win)
            
            # Удаляем из активных сделок
            if trade.id in self.active_trades:
                del self.active_trades[trade.id]
            
            await self._log_to_file(trade.id, f"✅ Позиция принудительно закрыта по истечении времени жизни")
            await self._log_trade_close_to_chat(trade, "Истечение времени жизни")
            
        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка принудительного закрытия позиции: {str(e)}")

    async def _cancel_all_orders(self, trade: TradeInstance):
        """Отменяет все открытые ордера сделки"""
        try:
            orders_to_cancel = []
            
            # Добавляем стоп-лосс в список для отмены
            if trade.stop_loss_order and trade.stop_loss_order.exchange_order_id:
                orders_to_cancel.append(('stop_loss', trade.stop_loss_order))
            
            # Добавляем тейк-профиты в список для отмены
            for i, tp_order in enumerate(trade.take_profit_orders):
                if tp_order.exchange_order_id and tp_order.status == OrderStatus.OPEN:
                    orders_to_cancel.append((f'take_profit_{i+1}', tp_order))
            
            # Добавляем ордера сетки для отмены (если есть)
            for i, grid_order in enumerate(trade.grid_orders):
                if grid_order.exchange_order_id and grid_order.status == OrderStatus.OPEN:
                    orders_to_cancel.append((f'grid_{i+1}', grid_order))
            
            # Отменяем все ордера
            for order_type, order in orders_to_cancel:
                try:
                    # Проверяем статус ордера перед отменой
                    try:
                        order_status = self.exchange.fetch_order(order.exchange_order_id, order.symbol)
                        current_status = order_status.get('status', '').lower()
                        
                        if current_status in ['closed', 'canceled', 'cancelled', 'filled']:
                            await self._log_to_file(trade.id, f"ℹ️ Ордер {order_type} уже {current_status}: {order.exchange_order_id}")
                            order.status = OrderStatus.CLOSED if current_status == 'filled' else OrderStatus.CANCELLED
                            continue
                    except Exception:
                        # Если не можем проверить статус, пытаемся отменить
                        pass
                    
                    # Отменяем ордер
                    self.exchange.cancel_order(order.exchange_order_id, order.symbol)
                    order.status = OrderStatus.CANCELLED
                    await self._log_to_file(trade.id, f"🗑️ Отменен {order_type} ордер: {order.exchange_order_id}")
                    
                except Exception as e:
                    error_msg = str(e).lower()
                    if 'not found' in error_msg or 'does not exist' in error_msg:
                        await self._log_to_file(trade.id, f"ℹ️ Ордер {order_type} уже не существует: {order.exchange_order_id}")
                        order.status = OrderStatus.CANCELLED
                    else:
                        await self._log_to_file(trade.id, f"⚠️ Ошибка отмены {order_type} ордера: {str(e)}")
            
        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка отмены ордеров: {str(e)}")

    async def _close_position_market_order(self, trade: TradeInstance, actual_position_size: float = None):
        """Закрывает позицию рыночным ордером"""
        try:
            if not trade.entry_order or not trade.entry_order.symbol:
                await self._log_to_file(trade.id, f"⚠️ Нет информации о позиции для закрытия")
                return
            
            # Определяем направление закрытия (противоположное открытию)
            if trade.signal.direction == SignalDirection.LONG:
                close_side = 'sell'  # Закрываем лонг продажей
            else:
                close_side = 'buy'   # Закрываем шорт покупкой
            
            # Используем актуальный размер позиции с биржи, если предоставлен
            if actual_position_size is not None and actual_position_size > 0:
                position_size = actual_position_size
                original_size = trade.entry_order.filled_amount or 0
                
                if abs(position_size - original_size) > 0.001:
                    await self._log_to_file(trade.id, f"📊 Используем актуальный размер с биржи: {position_size} (было {original_size})")
                else:
                    await self._log_to_file(trade.id, f"📊 Размер позиции не изменился: {position_size}")
            else:
                # Используем размер из локальных данных
                position_size = trade.entry_order.filled_amount
                await self._log_to_file(trade.id, f"📊 Используем размер из локальных данных: {position_size}")
            
            if position_size <= 0:
                await self._log_to_file(trade.id, f"⚠️ Размер позиции для закрытия = 0")
                return
            
            # Подготавливаем параметры для BingX hedge mode
            order_params = {
                'positionSide': 'LONG' if trade.signal.direction == SignalDirection.LONG else 'SHORT',
                'reduceOnly': True  # Только закрытие позиции
            }
            
            # Размещаем рыночный ордер на закрытие
            close_order = self.exchange.create_order(
                trade.entry_order.symbol,
                'market',
                close_side,
                position_size,
                None,  # market price
                order_params
            )
            
            # Получаем цену исполнения из ордера
            close_price = close_order.get('average') or close_order.get('price')
            if not close_price:
                # Если цена не доступна в ордере, пытаемся получить текущую рыночную цену
                try:
                    ticker = self.exchange.fetch_ticker(trade.entry_order.symbol)
                    close_price = ticker.get('last') or ticker.get('close')
                except:
                    close_price = None
                    
            # Рассчитываем realized PnL
            if close_price and trade.entry_price:
                trade.realized_pnl = self._calculate_realized_pnl(
                    trade.signal.direction,
                    trade.entry_price,
                    close_price,
                    position_size
                )
                
                pnl_emoji = "💚" if trade.realized_pnl > 0 else "❤️"
                await self._log_to_file(trade.id, f"{pnl_emoji} Realized PnL: ${trade.realized_pnl:.2f}")
            
            await self._log_to_file(trade.id, f"🔒 Позиция закрыта рыночным ордером: {close_side.upper()} {position_size} @ ${close_price or 'N/A'} (ID: {close_order['id']})")
            
        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка закрытия позиции: {str(e)}")

    def _calculate_realized_pnl(self, direction: 'SignalDirection', entry_price: float, exit_price: float, position_size: float) -> float:
        """Рассчитывает realized PnL для закрытой позиции
        
        Args:
            direction: Направление позиции (LONG/SHORT)
            entry_price: Цена входа
            exit_price: Цена выхода
            position_size: Размер позиции
            
        Returns:
            float: Realized PnL в USD
        """
        try:
            if not entry_price or not exit_price or not position_size:
                return 0.0
                
            # Рассчитываем PnL в зависимости от направления
            if direction.value.upper() == 'LONG':
                # Для лонга: прибыль когда цена выхода больше цены входа
                pnl_per_unit = exit_price - entry_price
            else:  # SHORT
                # Для шорта: прибыль когда цена выхода меньше цены входа
                pnl_per_unit = entry_price - exit_price
            
            # Общий PnL = PnL за единицу * размер позиции
            realized_pnl = pnl_per_unit * position_size
            
            return round(realized_pnl, 2)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Ошибка расчета realized PnL: {str(e)}")
            return 0.0

    async def monitor_trades(self):
        """Мониторинг активных сделок"""
        sync_counter = 0  # Счетчик для периодической синхронизации
        while True:
            try:
                # Проверяем, что exchange инициализирован
                if not self.exchange:
                    await asyncio.sleep(30)  # Ждем инициализации
                    continue

                # Периодическая синхронизация с биржей (каждые 5 минут)
                sync_counter += 1
                if sync_counter >= 30:  # 30 * 10 секунд = 5 минут
                    await self.sync_positions_with_exchange()
                    sync_counter = 0

                for trade_id, trade in list(self.active_trades.items()):
                    if trade.status != TradeStatus.ACTIVE:
                        continue

                    # Проверяем статус ордеров
                    await self._check_trade_orders(trade)

                await asyncio.sleep(10)  # Проверяем каждые 10 секунд

            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Ошибка мониторинга сделок: {str(e)}", e)
                await asyncio.sleep(60)

    async def _check_trade_orders(self, trade: TradeInstance):
        """Проверяет статус ордеров сделки"""
        try:
            # Проверяем время жизни позиции
            if trade.position_lifetime_seconds > 0:
                await self._check_position_lifetime(trade)
                
            # Проверяем тейк профиты
            tp_filled_count = 0
            for i, tp_order in enumerate(trade.take_profit_orders):
                if tp_order.status == OrderStatus.OPEN and tp_order.exchange_order_id:
                    try:
                        # Проверяем статус ордера на бирже
                        order_status = self.exchange.fetch_order(tp_order.exchange_order_id, tp_order.symbol)

                        if order_status.get('status') == 'closed':
                            tp_order.status = OrderStatus.CLOSED
                            tp_order.filled_at = datetime.now().isoformat()
                            tp_order.filled_amount = order_status.get('filled', tp_order.amount)
                            tp_filled_count += 1

                            # Рассчитываем PnL для частичного закрытия по TP
                            if trade.entry_price and tp_order.price:
                                partial_pnl = self._calculate_realized_pnl(
                                    trade.signal.direction,
                                    trade.entry_price,
                                    tp_order.price,
                                    tp_order.filled_amount
                                )
                                trade.realized_pnl += partial_pnl
                                
                                pnl_emoji = "💚" if partial_pnl > 0 else "❤️"
                                await self._log_to_file(trade.id, f"✅ Тейк профит {i+1} исполнен @ ${tp_order.price} ({pnl_emoji} +${partial_pnl:.2f})")
                                await self._log_to_chat(f"✅ Тейк профит {i+1} сделки {trade.id} исполнен @ ${tp_order.price} ({pnl_emoji} +${partial_pnl:.2f})")
                            else:
                                await self._log_to_file(trade.id, f"✅ Тейк профит {i+1} исполнен @ ${tp_order.price}")
                                await self._log_to_chat(f"✅ Тейк профит {i+1} сделки {trade.id} исполнен @ ${tp_order.price}")

                            # Если это первый тейк профит, перемещаем стоп в безубыток (если включено)
                            if tp_filled_count == 1 and not trade.breakeven_moved and trade.move_stop_to_breakeven:
                                await self._move_stop_to_breakeven(trade)

                    except Exception as e:
                        await self._log_to_file(trade.id, f"⚠️ Ошибка проверки TP {i+1}: {str(e)}")
                elif tp_order.status == OrderStatus.CLOSED:
                    tp_filled_count += 1

            # Проверяем сетку ордеров (для grid_open)
            if trade.open_mode == OpenMode.GRID_OPEN:
                filled_grid_orders = 0
                total_filled_amount = 0.0

                for i, grid_order in enumerate(trade.grid_orders):
                    if grid_order.status == OrderStatus.OPEN and grid_order.exchange_order_id:
                        try:
                            order_status = self.exchange.fetch_order(grid_order.exchange_order_id, grid_order.symbol)

                            if order_status.get('status') == 'closed':
                                grid_order.status = OrderStatus.CLOSED
                                grid_order.filled_at = datetime.now().isoformat()
                                grid_order.filled_amount = order_status.get('filled', grid_order.amount)
                                filled_grid_orders += 1
                                total_filled_amount += grid_order.filled_amount

                                await self._log_to_file(trade.id, f"✅ Ордер сетки {i+1}/10 исполнен @ ${grid_order.price}")

                        except Exception as e:
                            await self._log_to_file(trade.id, f"⚠️ Ошибка проверки ордера сетки {i+1}: {str(e)}")
                    elif grid_order.status == OrderStatus.CLOSED:
                        filled_grid_orders += 1
                        total_filled_amount += grid_order.filled_amount

                # Обновляем общую информацию о позиции
                if filled_grid_orders > 0 and trade.entry_order:
                    trade.entry_order.filled_amount = total_filled_amount
                    # Рассчитываем средневзвешенную цену входа
                    if total_filled_amount > 0:
                        weighted_price = sum(order.price * order.filled_amount for order in trade.grid_orders if order.status == OrderStatus.CLOSED) / total_filled_amount
                        trade.entry_price = weighted_price
                        trade.actual_volume_usd = trade.entry_price * total_filled_amount

            # Проверяем стоп лосс
            if trade.stop_loss_order and trade.stop_loss_order.status == OrderStatus.OPEN and trade.stop_loss_order.exchange_order_id:
                try:
                    order_status = self.exchange.fetch_order(trade.stop_loss_order.exchange_order_id, trade.stop_loss_order.symbol)

                    if order_status.get('status') == 'closed':
                        trade.stop_loss_order.status = OrderStatus.CLOSED
                        trade.stop_loss_order.filled_at = datetime.now().isoformat()

                        # Рассчитываем PnL для закрытия по SL
                        remaining_size = trade.entry_order.filled_amount or 0
                        # Вычитаем размеры уже исполненных TP
                        for tp in trade.take_profit_orders:
                            if tp.status == OrderStatus.CLOSED:
                                remaining_size -= tp.filled_amount or 0
                        
                        if trade.entry_price and trade.stop_loss_order.price and remaining_size > 0:
                            sl_pnl = self._calculate_realized_pnl(
                                trade.signal.direction,
                                trade.entry_price,
                                trade.stop_loss_order.price,
                                remaining_size
                            )
                            trade.realized_pnl += sl_pnl
                            
                            pnl_emoji = "💚" if sl_pnl > 0 else "❤️"
                            await self._log_to_file(trade.id, f"🛡️ Стоп лосс исполнен @ ${trade.stop_loss_order.price} ({pnl_emoji} ${sl_pnl:.2f})")
                            await self._log_to_chat(f"🛡️ Стоп лосс сделки {trade.id} исполнен @ ${trade.stop_loss_order.price} ({pnl_emoji} ${sl_pnl:.2f})")
                        else:
                            await self._log_to_file(trade.id, f"🛡️ Стоп лосс исполнен @ ${trade.stop_loss_order.price}")
                            await self._log_to_chat(f"🛡️ Стоп лосс сделки {trade.id} исполнен @ ${trade.stop_loss_order.price}")

                        # Закрываем сделку
                        await self.close_trade(trade.id, "Стоп лосс")

                except Exception as e:
                    await self._log_to_file(trade.id, f"⚠️ Ошибка проверки стоп лосса: {str(e)}")

        except Exception as e:
            await self._log_to_file(trade.id, f"❌ Ошибка проверки ордеров: {str(e)}")

    def get_active_trades(self) -> Dict[str, TradeInstance]:
        """Возвращает активные сделки"""
        return self.active_trades.copy()

    async def _log_trade_close_to_chat(self, trade: 'TradeInstance', reason: str):
        """Логирует подробную информацию о закрытии сделки в чат"""
        try:
            # Получаем текущий баланс
            current_balance = await self._get_usdt_balance()

            message = f"🔒 **СДЕЛКА ЗАКРЫТА**\n\n"
            message += f"**ID сделки:** `{trade.id}`\n"
            message += f"**Канал:** {trade.source_channel}\n"
            message += f"**Сигнал:** {trade.signal.direction.value.upper()} {trade.signal.ticker}\n"
            message += f"**Причина закрытия:** {reason}\n"

            if trade.entry_order:
                message += f"**Цена входа:** ${trade.entry_price or trade.signal.entry_price}\n"
                message += f"**Размер позиции:** {trade.entry_order.amount}\n"
                message += f"**Объем USD:** ${trade.actual_volume_usd:.2f}\n"

            # Информация о PnL (если доступна)
            if trade.realized_pnl != 0:
                pnl_emoji = "💚" if trade.realized_pnl > 0 else "❤️"
                message += f"{pnl_emoji} **Realized PnL:** ${trade.realized_pnl:.2f}\n"

            # Информация о времени
            if trade.created_at and trade.completed_at:
                from datetime import datetime
                try:
                    created = datetime.fromisoformat(trade.created_at)
                    completed = datetime.fromisoformat(trade.completed_at)
                    duration = completed - created
                    message += f"**Длительность:** {str(duration).split('.')[0]}\n"
                except:
                    pass

            # Текущий баланс
            message += f"\n💰 **Текущий баланс USDT:** ${current_balance:.2f}\n"

            await self._log_to_chat(message)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка логирования закрытия сделки в чат: {str(e)}", e)

    async def close_trade(self, trade_id: str, reason: str = "Manual close"):
        """Закрывает сделку (только локальное обновление статуса)"""
        try:
            if trade_id not in self.active_trades:
                return False

            trade = self.active_trades[trade_id]
            trade.status = TradeStatus.COMPLETED
            trade.completed_at = datetime.now().isoformat()

            await self._log_to_file(trade_id, f"🔒 Сделка закрыта: {reason}")

            # Обновляем статистику канала
            if trade.source_channel:
                is_win = trade.realized_pnl > 0
                await self._update_channel_stats(trade.source_channel, is_win)

            # Логируем подробную информацию о закрытии в чат
            await self._log_trade_close_to_chat(trade, reason)

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка закрытия сделки {trade_id}: {str(e)}", e)
            return False

    async def force_close_trade_on_exchange(self, trade_id: str, reason: str = "Manual close"):
        """Принудительно закрывает сделку на бирже с проверкой актуального размера позиции"""
        try:
            if trade_id not in self.active_trades:
                await self._log_to_file(trade_id, f"⚠️ Сделка {trade_id} не найдена в активных")
                return False

            trade = self.active_trades[trade_id]
            
            await self._log_to_file(trade_id, f"🔒 Начинаем принудительное закрытие: {reason}")
            
            # Проверяем актуальный статус позиции на бирже
            is_position_open, actual_position_size = await self._verify_position_still_open(trade)
            
            if is_position_open:
                await self._log_to_file(trade_id, f"✅ Позиция найдена на бирже - выполняем закрытие")
                # Принудительно закрываем с актуальным размером
                await self._force_close_position(trade, actual_position_size)
                return True
            else:
                await self._log_to_file(trade_id, f"ℹ️ Позиция уже закрыта на бирже - обновляем статус")
                # Просто обновляем локальный статус
                await self._update_trade_as_closed(trade, f"{reason} (позиция уже закрыта)")
                return True

        except Exception as e:
            await self._log_to_file(trade_id, f"❌ Ошибка принудительного закрытия: {str(e)}")
            if self.logger:
                await self.logger.error(f"Ошибка принудительного закрытия сделки {trade_id}: {str(e)}", e)
            return False

    async def _get_open_positions_from_exchange(self) -> List[Dict]:
        """Получает все открытые позиции с биржи"""
        try:
            if not self.exchange:
                if self.logger:
                    await self.logger.warning("Exchange не инициализирован, не могу получить позиции с биржи")
                return []

            # Определяем тестовый режим более безопасно
            is_testnet = False
            if hasattr(self, 'testnet'):
                is_testnet = self.testnet
            elif self.exchange_config and self.exchange_config.get('sandbox', False):
                is_testnet = True

            # В тестовом режиме пытаемся получить позиции, но если не получается - используем fallback
            if is_testnet:
                if self.logger:
                    await self.logger.info("Тестовый режим: пытаемся получить позиции с биржи")

            # Получаем все позиции с биржи (синхронный метод CCXT)
            positions = self.exchange.fetch_positions()

            if not positions:
                if self.logger:
                    await self.logger.info("Биржа вернула пустой список позиций")
                return []

            # Фильтруем только открытые позиции (с ненулевыми контрактами)
            open_positions = []
            for position in positions:
                if not isinstance(position, dict):
                    continue

                contracts = position.get('contracts', 0) or 0
                notional = position.get('notional', 0) or 0
                size = position.get('size', 0) or 0  # Альтернативное поле для размера позиции

                if contracts != 0 or abs(notional) > 0.01 or abs(size) > 0:  # Открытая позиция
                    open_positions.append(position)

            if self.logger:
                if len(open_positions) > 0:
                    position_info = []
                    for pos in open_positions:
                        symbol = pos.get('symbol', 'Unknown')
                        side = pos.get('side', 'Unknown')
                        contracts = pos.get('contracts', 0)
                        notional = pos.get('notional', 0)
                        size = pos.get('size', 0)
                        position_info.append(f"{symbol} {side} contracts:{contracts} size:{size} (${abs(notional):.2f})")
                    await self.logger.info(f"Найдено {len(open_positions)} открытых позиций: {', '.join(position_info)}")
                else:
                    await self.logger.info("Открытых позиций не найдено")

            return open_positions

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка получения позиций с биржи: {str(e)}", e)
            return []

    async def _calculate_total_portfolio_usage(self, total_balance: float, excluding_trade_id: Optional[str] = None) -> tuple[float, float, int]:
        """Вычисляет общее использование портфеля всеми активными позициями с биржи

        ВАЖНО: Считает ЗАЛОГ (margin), а не номинальную стоимость позиций!

        Args:
            total_balance: Общий баланс портфеля
            excluding_trade_id: ID сделки, которую нужно исключить из подсчета (используется для fallback)

        Returns:
            tuple: (общий_залог_в_долларах, процент_от_портфеля, количество_позиций)
        """
        try:
            # Получаем реальные открытые позиции с биржи
            open_positions = await self._get_open_positions_from_exchange()

            total_margin_usd = 0.0  # Общий залог всех позиций
            positions_count = len(open_positions)

            # Если удалось получить позиции с биржи
            if open_positions:
                if self.logger:
                    await self.logger.info(f"Обрабатываем {len(open_positions)} позиций для расчета объема")

                for position in open_positions:
                    symbol = position.get('symbol', 'Unknown')
                    # Получаем размер позиции в USD - пробуем разные поля
                    notional_usd = position.get('notional', 0.0) or 0.0
                    calculation_method = "notional"

                    # Если notional не доступен, пытаемся рассчитать из size и mark price
                    if not notional_usd:
                        size = position.get('size', 0.0) or 0.0
                        mark_price = position.get('markPrice', 0.0) or position.get('mark_price', 0.0) or 0.0
                        if size and mark_price:
                            notional_usd = abs(size * mark_price)
                            calculation_method = f"size({size}) * markPrice({mark_price})"

                    # Если всё ещё нет данных, пробуем contracts
                    if not notional_usd:
                        contracts = position.get('contracts', 0.0) or 0.0
                        contract_size = position.get('contractSize', 1.0) or 1.0
                        mark_price = position.get('markPrice', 0.0) or position.get('mark_price', 0.0) or 0.0
                        if contracts and mark_price:
                            notional_usd = abs(contracts * contract_size * mark_price)
                            calculation_method = f"contracts({contracts}) * contractSize({contract_size}) * markPrice({mark_price})"

                    if notional_usd > 0.01:  # Минимальный порог $0.01
                        # ВАЖНО: Для расчета использования портфеля нужен ЗАЛОГ, а не номинальная стоимость
                        # Получаем залог (margin) из позиции
                        margin_usd = position.get('initialMargin', 0.0) or position.get('margin', 0.0) or 0.0

                        # Если залог не доступен, рассчитываем его как номинальная_стоимость / плечо
                        if not margin_usd and notional_usd > 0:
                            # Пытаемся получить плечо из позиции
                            leverage = position.get('leverage', 0.0) or 1.0
                            if leverage > 0:
                                margin_usd = notional_usd / leverage
                            else:
                                # Fallback: используем номинальную стоимость (консервативный подход)
                                margin_usd = notional_usd

                        total_margin_usd += margin_usd
                        if self.logger:
                            await self.logger.info(f"Позиция {symbol}: ${notional_usd:.2f} (notional), ${margin_usd:.2f} (margin)")
                    elif self.logger:
                        # Логируем доступные поля для отладки
                        available_fields = {k: v for k, v in position.items() if v is not None and v != 0}
                        await self.logger.warning(f"Не удалось рассчитать объем для {symbol}. Доступные поля: {available_fields}")
            else:
                # Fallback: используем данные из отслеживаемых сделок
                if self.logger:
                    await self.logger.info("Используем fallback: подсчет позиций из отслеживаемых сделок")

                positions_count = 0
                for trade_id, trade in self.active_trades.items():
                    # Исключаем конкретную сделку, если указано
                    if excluding_trade_id and trade_id == excluding_trade_id:
                        continue

                    # Учитываем только активные сделки с фактическим объемом
                    if trade.status == TradeStatus.ACTIVE and trade.actual_volume_usd > 0:
                        # Для fallback используем фактический залог (actual_volume_usd / leverage)
                        margin_from_trade = trade.actual_volume_usd / (trade.signal.leverage or 1)
                        total_margin_usd += margin_from_trade
                        positions_count += 1

            # Вычисляем процент от общего баланса
            portfolio_percent = (total_margin_usd / total_balance * 100) if total_balance > 0 else 0.0

            return total_margin_usd, portfolio_percent, positions_count

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка вычисления общего использования портфеля: {str(e)}", e)
            return 0.0, 0.0, 0 

    async def _update_channel_stats(self, source_channel: str, is_win: bool):
        """Обновляет статистику wins/fails для канала в source_channels.json
        
        Args:
            source_channel: Название канала
            is_win: True если позиция закрыта в плюс, False если в минус
        """
        try:
            import json
            import os
            
            channels_file = "source_channels.json"
            
            # Проверяем существование файла
            if not os.path.exists(channels_file):
                if self.logger:
                    await self.logger.warning(f"Файл {channels_file} не найден, пропускаем обновление статистики")
                return
            
            # Читаем текущую конфигурацию
            with open(channels_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Проверяем наличие канала в конфигурации
            if 'channels' not in data or source_channel not in data['channels']:
                if self.logger:
                    await self.logger.warning(f"Канал {source_channel} не найден в {channels_file}")
                return
            
            channel_config = data['channels'][source_channel]
            
            # Инициализируем поля если их нет
            if 'wins' not in channel_config:
                channel_config['wins'] = 0
            if 'fails' not in channel_config:
                channel_config['fails'] = 0
            if 'wins_ratio' not in channel_config:
                channel_config['wins_ratio'] = 0.0
            
            # Обновляем статистику
            if is_win:
                channel_config['wins'] += 1
                stat_type = "WIN"
                emoji = "💚"
            else:
                channel_config['fails'] += 1
                stat_type = "FAIL"
                emoji = "❤️"
            
            # Рассчитываем процент побед
            total_trades = channel_config['wins'] + channel_config['fails']
            win_rate = (channel_config['wins'] / total_trades * 100) if total_trades > 0 else 0.0
            
            # Сохраняем wins_ratio в конфигурации
            channel_config['wins_ratio'] = round(win_rate, 1)
            
            # Сохраняем обновленную конфигурацию
            with open(channels_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Логируем обновление
            await self._log_to_file(
                f"stats_update_{source_channel}",
                f"{emoji} {stat_type} для {source_channel}: {channel_config['wins']}W/{channel_config['fails']}L (WR: {channel_config['wins_ratio']:.1f}%)"
            )
            
            if self.logger:
                await self.logger.info(f"Обновлена статистика {source_channel}: {channel_config['wins']}W/{channel_config['fails']}L ({channel_config['wins_ratio']:.1f}%)")
                
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка обновления статистики канала {source_channel}: {str(e)}", e)

    async def sync_positions_with_exchange(self):
        """Синхронизирует позиции с биржей и обнаруживает позиции, открытые вручную"""
        try:
            # Получаем позиции с биржи
            exchange_positions = await self._get_open_positions_from_exchange()
            
            # Получаем отслеживаемые позиции
            tracked_positions = {trade.signal.ticker: trade for trade in self.active_trades.values() if trade.status == TradeStatus.ACTIVE}
            
            # Находим позиции на бирже, которые не отслеживаются системой
            untracked_positions = []
            for position in exchange_positions:
                symbol = position.get('symbol', '')
                # Убираем суффикс :USDT для сравнения
                clean_symbol = symbol.replace(':USDT', '')
                
                if clean_symbol not in tracked_positions:
                    size = position.get('size', 0.0) or 0.0
                    notional = position.get('notional', 0.0) or 0.0
                    mark_price = position.get('markPrice', 0.0) or 0.0
                    
                    if abs(size) > 0.001:  # Минимальный размер позиции
                        untracked_positions.append({
                            'symbol': clean_symbol,
                            'size': size,
                            'notional': notional,
                            'mark_price': mark_price,
                            'side': 'LONG' if size > 0 else 'SHORT'
                        })
            
            # Если найдены неотслеживаемые позиции, отправляем уведомление
            if untracked_positions:
                warning_message = "⚠️ **ОБНАРУЖЕНЫ ПОЗИЦИИ, ОТКРЫТЫЕ ВРУЧНУЮ**\n\n"
                warning_message += "Следующие позиции существуют на бирже, но не отслеживаются системой:\n\n"
                
                for pos in untracked_positions:
                    warning_message += f"**{pos['symbol']}** ({pos['side']})\n"
                    warning_message += f"Размер: {abs(pos['size']):.4f}\n"
                    warning_message += f"Стоимость: ${pos['notional']:.2f}\n"
                    warning_message += f"Цена: ${pos['mark_price']:.4f}\n\n"
                
                warning_message += "**Рекомендации:**\n"
                warning_message += "1. Закройте эти позиции вручную в интерфейсе BingX\n"
                warning_message += "2. Используйте только торговую систему для открытия позиций\n"
                warning_message += "3. Проверьте настройки лимитов портфеля\n"
                
                await self._log_to_chat(warning_message)
                
                if self.logger:
                    await self.logger.warning(f"Обнаружено {len(untracked_positions)} позиций, открытых вручную")
            
            return untracked_positions
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка синхронизации позиций с биржей: {str(e)}", e)
            return []
